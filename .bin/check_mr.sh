#!/usr/bin

# 检查是否存在未合并的MR

PROJECT_ID=626
PRIVATE_TOKEN="Wq-LGQakcZuaJiuR8zYM"

# 钉钉
DD=1
DD_AT_ALL="false"
DD_TITLE="合并请求通知"
DD_KEYWORD="部署服务报错"
DD_REASON="Merge_Request_待合并至主分支~"
GITLAB_HOST="https://gitlab.meishubao.com/"
CI_MERGE_REQUESTS_URL="${CI_PROJECT_URL}/-/merge_requests"
DD_AT_MOBILES_STRING="@18695878741@18301307570"
DD_AT_MOBILES_ARRAY="['18695878741','18301307570']"
DD_TOKEN="5db33f2369be2505bc21939f62ef18cc2e3f74a22915150bac8b9ab4a3b77375"

# 拉取MR
MR_RESULT=$(curl --request GET --header "PRIVATE-TOKEN: ${PRIVATE_TOKEN}" "${GITLAB_HOST}/api/v4/projects/${PROJECT_ID}/merge_requests?state=opened")
echo "${MR_RESULT}"
EMPTY="[]"

# 判断是否为空，不为空中断build
if [ "$MR_RESULT" != "$EMPTY" ];then
  echo '\033[31m MR扫描: 扫描到当前项目存在opened的MR，请将MR合并至master，再拉取最新代码生成Tag重新上线！(ERROR) \033[0m'
  if [ "$DD" == 1 ];then
    # DD DATA
    DATA="{'msgtype': 'markdown','markdown': {'title':'${DD_TITLE}','text': '#### **${DD_KEYWORD}**\n --- \n > - 构建项目: ${CI_PROJECT_NAME} \n > -  构建分支: [${CI_COMMIT_TAG}](${CI_MERGE_REQUESTS_URL}) \n > - 报错原因: ${DD_REASON} \n\n ${DD_AT_MOBILES_STRING}'},'at': {'isAtAll': ${DD_AT_ALL}, 'atMobiles':${DD_AT_MOBILES_ARRAY}}}"
    # DD API
    curl "https://oapi.dingtalk.com/robot/send?access_token=${DD_TOKEN}" -H "Content-Type: application/json" -d "${DATA}"
  fi
  # 中断！
  exit 1;
else
  echo '\033[32m MR扫描: MR检查通过，未找到状态为opened的MR！(SUCCESS) \033[0m'
fi
