{"name": "msb-global-website", "version": "1.0.0", "description": "A Vue.js project", "author": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev": "vite --mode development", "build:dev": "tsc && vite build --mode dev", "build:test": "tsc && vite build --mode test", "build:live": "tsc && vite build --mode live", "prettier": "npx prettier --write ."}, "dependencies": {"dayjs": "^1.11.13", "google-libphonenumber": "^3.2.10", "msb-public-library": "git+https://opserver:<EMAIL>/msb-fe/msb-public-library.git#0.0.58", "tailwindcss": "3.1.8", "video.js": "^7.7.5", "vue": "^2.5.2", "vue-i18n": "8", "vue-router": "^3.0.1"}, "devDependencies": {"@msb-next/vite-plugins": "^1.3.0", "@types/google-libphonenumber": "^7.4.30", "@types/node": "14.14.31", "autoprefixer": "^7.1.2", "axios": "^0.19.2", "less": "^3.10.3", "less-loader": "^5.0.0", "postcss-import": "^11.0.0", "postcss-url": "^7.2.1", "prettier": "^3.0.3", "terser": "^5.39.0", "typescript": "4.3.2", "vite": "2", "vite-plugin-components": "^0.13.3", "vite-plugin-vue2": "2.0.3", "vue-meta-info": "^0.1.7", "vue-template-compiler": "^2.5.2"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}