import { defineConfig, loadEnv } from "vite";
import { createVuePlugin } from "vite-plugin-vue2";
import ViteComponents from "vite-plugin-components";
import { resolve } from "path";
import { UploadPlugin } from "@msb-next/vite-plugins";
import pkg from "./package.json";
import dayjs from "dayjs";

export default async ({ mode }) => {
  process.env = {
    ...process.env,
    ...loadEnv(mode, process.cwd()),
    VITE_APP_TIME: `v.${pkg.version}.${dayjs().format("YY.MMDD.HHmm")}`,
    VITE_APP_STARTYEAR: "2014",
    VITE_APP_THISYEAR: dayjs().format("YYYY")
  };
  const isProd = process.env.VITE_APP_ENV && process.env.VITE_APP_ENV !== "dev";
  const plugins = [
    createVuePlugin({
      vueTemplateOptions: {
        compiler: require("vue-template-babel-compiler")
      }
    }),
    ViteComponents({ transformer: "vue2" })
  ];
  let base = "/";
  if (isProd) {
    const prefix = `${pkg.name}/${process.env.VITE_APP_ENV}/${pkg.version}`;
    base = `https://imgvip.meishubao.com/${prefix}/`;
    plugins.push(
      UploadPlugin({
        bucket: "one-by-one", // oss bucket
        prefix // 自定义路径前缀
      })
    );
  }
  return defineConfig({
    root: __dirname,
    resolve: {
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
      alias: [
        { find: /^~/, replacement: "" },
        { find: "@", replacement: resolve(__dirname, "src") },
        { find: "i18n", replacement: resolve(__dirname, "src/locales") }
      ]
    },
    plugins,
    base,
    build: {
      outDir: `dist`,
      minify: true,
      sourcemap: process.env.VITE_APP_ENV !== "live",
      commonjsOptions: {
        transformMixedEsModules: true
      }
    },
    server: {
      port: 3001,
      host: '0.0.0.0',
      proxy: {
        "^/vip121": {
          target: "https://test121.meishubao.com",
          changeOrigin: true,
          rewrite: path => path.replace(/^\/vip121/, "")
        },
        "^/feedback": {
          target: "http://new.msbart.meishubao.com",
          changeOrigin: true,
          rewrite: path => path.replace(/^\/feedback/, "")
        },
        "^/onlineapi": {
          target: "https://testapi.meishubao.com",
          changeOrigin: true,
          rewrite: path => path.replace(/^\/onlineapi/, "")
        }
      }
    }
  });
};
