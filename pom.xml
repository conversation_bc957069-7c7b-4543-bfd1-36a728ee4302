<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.meishubao</groupId>
    <artifactId>msb-global-website</artifactId>
    <version>1.0.4</version>
    <packaging>pom</packaging>

    <properties>
        <jkube.debug.enabled>false</jkube.debug.enabled>
        <jkube.docker.verbose>false</jkube.docker.verbose>
        <jkube.docker.logStdout>true</jkube.docker.logStdout>
        <maven.deploy.skip>true</maven.deploy.skip>
        <docker.command>docker</docker.command>
        <!-- 重要提示：-->
        <!-- 全局配置 'jkube.enricher.jkube-well-known-labels.enabled' 应设为 'false'。-->
        <!-- 如果在升级 'kubernetes-maven-plugin' 版本后，此选项被设为 'true'，可能会导致发布后的 Kubernetes 资源（如 Service）变得无法访问。-->
        <!-- 这是因为 Kubernetes 的 Service 是不可变的，一旦创建就不能修改。-->
        <jkube.enricher.jkube-well-known-labels.enabled>false</jkube.enricher.jkube-well-known-labels.enabled>
    </properties>

    <distributionManagement>
        <repository>
            <id>${releases.id}</id>
            <name>${releases.name}</name>
            <url>${releases.url}</url>
        </repository>
        <snapshotRepository>
            <id>${snapshots.id}</id>
            <name>${snapshots.name}</name>
            <url>${snapshots.url}</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <artifactId>exec-maven-plugin</artifactId>
                <groupId>org.codehaus.mojo</groupId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>exec-docker-login</id>
                        <phase>deploy</phase>
                        <goals>
                        <goal>exec</goal>
                        </goals>
                        <configuration>
                        <executable>docker</executable>
                        <workingDirectory>${project.basedir}</workingDirectory>
                        <arguments>
                            <argument>login</argument>
                            <argument>--username</argument>
                            <argument>opadmin</argument>
                            <argument>--password</argument>
                            <argument>o14HFGD2tMpV</argument>
                            <argument>harbor.meishubao.com</argument>
                        </arguments>
                        </configuration>
                    </execution>  
                    <execution>
                        <id>exec-docker-build</id>
                        <phase>install</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>docker</executable>
                            <arguments>
                                <argument>build</argument>
                                <argument>--build-arg</argument>
                                <argument>BASE_VERSION=${BASE_VERSION}</argument>
                                <argument>-t</argument>
                                <argument>${docker.prefix}/${project.artifactId}:${KUBERNETES_NAMESPACE}-${project.version}</argument>
                                <argument>.</argument>
                            </arguments>
                            <workingDirectory>${project.basedir}</workingDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>exec-docker-push</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>docker</executable>
                            <arguments>
                                <argument>push</argument>
                                <argument>${docker.prefix}/${project.artifactId}:${KUBERNETES_NAMESPACE}-${project.version}</argument>
                            </arguments>
                            <workingDirectory>${project.basedir}</workingDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.eclipse.jkube</groupId>
                <artifactId>kubernetes-maven-plugin</artifactId>
                <version>1.16.2</version>
                <executions>
                    <execution>
                        <id>jkube</id>
                        <goals>
                            <goal>resource</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <enricher>
                        <config>
                            <jkube-revision-history>
                                <limit>10</limit>
                            </jkube-revision-history>
                        </config>
                    </enricher>
                    <images>
                        <image>
                            <name>${docker.prefix}/${project.artifactId}:${KUBERNETES_NAMESPACE}-${project.version}</name>
                            <build>
                                <tags>
                                    <tag>latest</tag>
                                </tags>
                                <ports>
                                    <port>80</port>
                                </ports>
                            </build>
                        </image>
                    </images>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
