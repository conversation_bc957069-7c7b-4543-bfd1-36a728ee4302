stages:
  - build
  - deploy
  - merge-request

.build:
  stage: build
  tags:
    - msb-fe
  script:
    - nvm use v14.17.0
    - node -v
    - yarn --prefer-offline --no-progress
    - export NODE_OPTIONS=--max-old-space-size=32768
    - yarn build:${BASE_VERSION}
  cache:
    key: ${CI_COMMIT_REF_NAME}
    paths:
      - dist/
      - node_modules

.deploy:
  stage: deploy
  tags:
    - msb-fe
  variables:
    DEPLOY_USER: "yelei gaodongyu opserver liwei"
    KUBECTL_CLI_OPTS: 'kubectl --kubeconfig=/home/<USER>/tengxuncloud-config'
    KUBECTL_CLI_OPTS_LIVE: 'kubectl --kubeconfig=/home/<USER>/k8s-live-txcloud-config'  
    PODS: 1
  cache:
    key: ${BASE_VERSION}
    paths:
      - dist/
      - node_modules
    policy: pull
  script:
    - ./mvnw versions:set -DartifactId=$CI_PROJECT_NAME -DnewVersion=$(date "+%Y%m%d%H%M%S")
    - export KUBERNETES_NAMESPACE=${KUBERNETES_NAMESPACE_VALUE}
    - export BASE_VERSION=${BASE_VERSION}
    - echo ${KUBECTL_CLI_OPTS_CONFIG}
    - ${KUBECTL_CLI_OPTS_CONFIG}  get namespaces
    - ./mvnw deploy -Ddocker.prefix=harbor.meishubao.com/xxms -Ddocker.registry=harbor.meishubao.com
    - "envsubst < target/classes/META-INF/jkube/kubernetes.yml | sed 's/name: '$CI_PROJECT_NAME'/name: '${VERSION_NAME}'/g' | sed 's/replicas: 1/replicas: '${PODS}'/g' | sed 's/app: '$CI_PROJECT_NAME'/app: '${VERSION_NAME}'/g' | sed 's/memory: 2048Mi/memory: 1024Mi/g' | sed 's/cpu: 1000m/cpu: 100m/g'  | ${KUBECTL_CLI_OPTS_CONFIG} apply -f - -n ${KUBERNETES_NAMESPACE}"
  allow_failure: false
  cache:
    key: ${CI_COMMIT_REF_NAME}
    paths:
      - dist/

build:test:
  extends: .build
  variables:
    BASE_VERSION: test
  only:
    - test
  except:
    - tags

build:live:
  extends: .build
  variables:
    BASE_VERSION: live
  only:
    - /^msb-global-website_.*$/
  except:
    - branchs

deploy:test:
  extends: .deploy
  variables:
    KUBERNETES_NAMESPACE_VALUE: 1v1-frontend-test
    VERSION_NAME: msb-global-website-test
    BASE_VERSION: test
    KUBECTL_CLI_OPTS_CONFIG: ${KUBECTL_CLI_OPTS}
  after_script:
    - echo $KUBECTL_CLI_OPTS_CONFIG $POD
    - ${KUBECTL_CLI_OPTS_CONFIG} patch deployment ${VERSION_NAME} -p '{"spec":{"template":{"spec":{"containers":[{"name":"'${VERSION_NAME}'","resources":{"requests":{"cpu":"100m","memory":"100Mi"}}}]}}}}' -n ${KUBERNETES_NAMESPACE_VALUE}
  only:
    - test
  except:
    - tags

deploy:live:
  extends: .deploy
  variables:
    PODS: 2
    KUBERNETES_NAMESPACE_VALUE: frontend
    VERSION_NAME: msb-global-website
    BASE_VERSION: live
    KUBECTL_CLI_OPTS_CONFIG: ${KUBECTL_CLI_OPTS_LIVE}
  allow_failure: false
  before_script:
    - export result=$(echo $DEPLOY_USER | grep $GITLAB_USER_NAME)
    - if [ "$CI_JOB_NAME" == "deploy:prod" -a "$result" == "" ]; then echo '没有权限' && exit 1; fi
    - bash .bin/check_mr.sh ${CI_COMMIT_SHA}
  only:
    - /^msb-global-website_.*$/
  except:
    - branchs
  when: manual

### merge-request ###
merge-request:
  stage: merge-request
  tags:
    - msb-fe
  script:
    - bash .bin/push_mr.sh ${CI_COMMIT_SHA}
  only:
    - /^msb-global-website_.*$/
  except:
    - branchs
