<!doctype html>
<html>
  <head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-MCZD2G2Q');</script>
    <!-- End Google Tag Manager -->

    <meta charset="utf-8" />
    <meta name="applicable-device" content="pc" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link
      rel="shortcut icon"
      type="image/x-icon"
      href="https://global-static.meishubao.com/sea_web/img/msb_global.ico"
    />
    <title>美术宝Global-在线学画画</title>
  </head>

  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MCZD2G2Q"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <div id="app"></div>
    <!-- built files will be auto injected -->

    <div id="fb-root"></div>

    <!-- Your 聊天插件 code -->
    <div id="fb-customer-chat" class="fb-customerchat"></div>
    <script>
      if (this === self) {
        var chatbox = document.getElementById("fb-customer-chat");
        chatbox.setAttribute("page_id", "111860540302880");
        chatbox.setAttribute("attribution", "biz_inbox");
        window.fbAsyncInit = function () {
          FB.init({
            xfbml: true,
            version: "v12.0"
          });
        };
        (function (d, s, id) {
          var js,
            fjs = d.getElementsByTagName(s)[0];
          if (d.getElementById(id)) return;
          js = d.createElement(s);
          js.id = id;
          js.src =
            "https://connect.facebook.net/en_US/sdk/xfbml.customerchat.js";
          fjs.parentNode.insertBefore(js, fjs);
        })(document, "script", "facebook-jssdk");
      }
    </script>
    <script>
      function getParam(p) {
        var match = RegExp("[?&]" + p + "=([^&]*)").exec(
          window.location.search
        );
        return match && decodeURIComponent(match[1].replace(/\+/g, " "));
      }

      function getExpiryRecord(value) {
        var expiryPeriod = 90 * 24 * 60 * 60 * 1000; // 以毫秒计的 90 天有效期
        var expiryDate = new Date().getTime() + expiryPeriod;
        return {
          value: value,
          expiryDate: expiryDate
        };
      }

      function addGclid() {
        var gclidParam = getParam("gclid");
        var gclidFormFields = ["gclid_field", "foobar"]; // 此处为所有可能的 GCLID 表单字段
        var gclidRecord = null;
        var currGclidFormField;
        var gclsrcParam = getParam("gclsrc");
        var isGclsrcValid = !gclsrcParam || gclsrcParam.indexOf("aw") !== -1;
        gclidFormFields.forEach(function (field) {
          if (document.getElementById(field)) {
            currGclidFormField = document.getElementById(field);
          }
        });
        if (gclidParam && isGclsrcValid) {
          gclidRecord = getExpiryRecord(gclidParam);
          localStorage.setItem("gclid", JSON.stringify(gclidRecord));
        }

        var gclid = gclidRecord || JSON.parse(localStorage.getItem("gclid"));
        var isGclidValid = gclid && new Date().getTime() < gclid.expiryDate;

        if (currGclidFormField && isGclidValid) {
          currGclidFormField.value = gclid.value;
        }
      }
      window.addEventListener("load", addGclid);
    </script>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
