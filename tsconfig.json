{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "noEmit": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "i18n": ["src/locales"]}, "lib": ["esnext", "dom"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules"]}