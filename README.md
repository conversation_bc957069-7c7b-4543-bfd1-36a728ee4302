# apps

> A Vue.js project

## Build Setup

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report
```

### 部署方式
该项目使用jenkins构建异常，需本地执行yarn build，产出dist文件夹，push到远程开发分支，然后当前分支tag，交给运维部署jenkins，手动提交merge request。
执行yarn build前，要执行rm -rf dist,确保本次build产出是最新的dist。

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).

# 部署

```
test:
dev分支
http://**************:8090/job/users-global-web-test/ 
https://global.meishubao.com/
？

live:
tag: website_20211204_v12
http://***********:8090/job/PRO-Fe-hw-msb-global-web/
https://global.meishubao.com/
```