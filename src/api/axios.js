import { t } from "i18n";
import axios from "axios";

axios.interceptors.request.use(
  config => {
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
//  RESPONSE 响应异常拦截
axios.interceptors.response.use(
  data => {
    return data;
  },
  error => {
    $vue.$toast(t("操作失败"));
    return Promise.reject(error);
  }
);
const http = config => {
  let baseUrl;
  if (process.env.NODE_ENV === "development") {
    baseUrl = config.server ? config.server : "/vip121";
  } else if (process.env.NODE_ENV === "production") {
    if (config.server === "/feedback") {
      baseUrl = "https://msbartglobal.meishubao.com";
    } else {
      baseUrl = config.server;
    }
  }
  axios.defaults.baseURL = baseUrl;
  return new Promise((resolve, reject) => {
    let params = {
      method: config.method ? config.method : "get",
      url: config.url,
      headers: {
        "Content-Type": config.type ? config.type : "application/json",
        Authorization: localStorage.getItem("token")
          ? localStorage.getItem("token")
          : ""
      }
    };
    if (config.method) {
      params.data = config.data;
    } else {
      params.params = config.data;
    }
    axios(params)
      .then(res => {
        resolve(res);
      })
      .then(err => {
        reject(err);
      });
  });
};
export { axios, http };
