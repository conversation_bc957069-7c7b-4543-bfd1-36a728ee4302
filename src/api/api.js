import { http } from "./axios";
import qs from "qs";

export const submitPhone = data => {
  return http({
    url: "/api/ad/report",
    method: "post",
    type: "application/x-www-form-urlencoded",
    data: qs.stringify(data),
    server: "/vip121"
  });
};

export const submitFeedback = data => {
  return http({
    url: "/contactUs",
    method: "post",
    data: data,
    server: "/feedback"
  });
};
export const getIplLocation = () => {
  return http({
    url: "/api/third/ip_location",
    method: "post",
    type: "application/x-www-form-urlencoded",
    server: "/onlineapi"
  });
};
export const getVersion = data => {
  return http({
    url: "/version",
    method: "post",
    data: qs.stringify(data),
    type: "application/x-www-form-urlencoded",
    server: "/onlineapi"
  });
};
