import Vue from "vue";
import VueI18n from "vue-i18n";
import zhCN from "./zh-CN.json";
import zhTW from "./zh-HK.json";

Vue.use(VueI18n);

// 注意：属性名 是不同语言的标识，用于后续切换时传递使用。
const messages = {
  'zh-CN': zhCN as any,
  'zh-HK': zhTW as any
};

const i18n = new VueI18n({
  // 设置默认语言为中文
  locale: "zh-CN",
  messages: messages
});

export const t = i18n.t.bind(i18n);

// 切换
// i18n.locale = 'zh-HK'

export default i18n;
