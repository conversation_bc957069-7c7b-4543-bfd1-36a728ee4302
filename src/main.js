// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import App from "./App";
import router from "./router";
import importDirective from "@/directive";
import "video.js/dist/video-js.css";
import MetaInfo from "vue-meta-info";
import Toast from "./components/toast";
import i18n from "./locales";

Vue.use(Toast);
Vue.use(MetaInfo);
importDirective(Vue);

Vue.config.productionTip = false;
router.afterEach((to, from, next) => {
  window.scrollTo(0, 0);
});
Vue.prototype.$getScrollbarWidth = () => {
  const scrollDiv = document.createElement("div");
  scrollDiv.style.overflow = "scroll";
  document.body.appendChild(scrollDiv);
  const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;
  document.body.removeChild(scrollDiv);
  return scrollbarWidth || "17";
};

window.$vue = new Vue({
  router,
  i18n,
  render: h => h(App)
}).$mount("#app");
