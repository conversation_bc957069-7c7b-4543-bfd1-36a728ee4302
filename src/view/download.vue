<template>
  <div class="flex flex-col m-0 p-0">
    <nav-head></nav-head>
    <div class="bg1 w-full h-[950px] pt-[233px]">
      <div class="w-[1200px] mx-auto flex">
        <div class="flex-1">
          <div class="mt-[45px] font-medium text-[38px] text-white leading-[53px]">美術寶應用程式</div>
          <div class="mt-[13px] font-normal text-[20px] text-white leading-[28px]">跨平台授課為你提供優質上課環境</div>
        </div>
        <div class="w-[180px] h-[240px] rounded-[26px] bg-white pt-[16px] flex flex-col items-center bg-wwhite">
          <img class="w-[148px] h-[150px]" src="@/assets/images/home/<USER>/download/1对1二维码@2x.png" alt="" />
          <div class="mt-[11px] font-normal text-[18px] text-[#969696] leading-[25px]">掃描二維碼下載</div>
          <div class="font-normal text-[18px] text-[#969696] leading-[25px]">手機應用程式</div>
        </div>
        <div class="cursor-pointer anim_btn select-none ml-[38px] w-[260px] h-[240px] rounded-[26px] bg-white pt-[58px]">
          <a class="w-full h-full flex flex-col items-center" href="https://itunes.apple.com/cn/app/%E7%BE%8E%E6%9C%AF%E5%AE%9D1%E5%AF%B91-%E5%B0%91%E5%84%BF%E5%9C%A8%E7%BA%BF%E5%AD%A6%E7%94%BB%E7%94%BB%E5%AD%A6%E7%BE%8E%E6%9C%AF/id1362507427?mt=8" target="_blank">
            <img class="w-[74px] h-[96px]" src="@/assets/images/home//hk/download/ipad图标@2x.png" alt="" />
            <div class="mt-[35px] font-normal text-[18px] text-[#969696] leading-[25px]">下載iPad應用程式</div>
          </a>
        </div>
        <div class="cursor-pointer anim_btn select-none ml-[28px] w-[260px] h-[240px] rounded-[26px] bg-white pt-[65px]">
          <a :href="url" class="w-full h-full flex flex-col items-center">
            <img class="w-[112px] h-[74px]" src="@/assets/images/home//hk/download/image.png" alt="" />
            <div class="mt-[41px] font-normal text-[18px] text-[#969696] leading-[25px]">{{ txt }}</div>
          </a>
        </div>
      </div>
    </div>
    <nav-foot></nav-foot>
  </div>
</template>
<script>
import navHead from "@/components/headHk";
import navFoot from "@/components/footHk";
import { t } from "i18n";
import { getVersion } from "@/api/api.js";

export default {
  metaInfo: {
    title: "Artworld美術寶-應用程式下載-手機、iPad、Mac、Wiindows應用程式下載",
    meta: [
      {
        name: "keywords",
        content: "美術寶HongKong,美術寶,美術寶1對1,美術寶1on1,美術寶一對一,少兒美術,在線少兒美術,兒童美術,在線兒童美術,美術寶Global,美術寶1對1香港官網"
      },
      {
        name: "description",
        content: "Artworld美術寶是擁有數千名美院專業教師的在線兒童美術畫畫平台！致力於為每個家庭提供個性化定製的美育課程，主要面向4-18歲適齡兒童和青少年，采用1對1真人在線互動教學形式，利用iPad、電腦等終端，隨時隨地的提供少兒藝術培養專屬課程。"
      }
    ]
  },
  data() {
    return {
      url: "",
      txt: "下載Mac應用程式"
    };
  },
  components: {
    navHead,
    navFoot
  },
  methods: {
    getOS() {
      var isMac = /macintosh|mac os x/i.test(navigator.userAgent);
      return isMac ? "mac" : "win";
    },
    version() {
      var os = this.getOS();
      this.txt = os === "mac" ? "下載Mac應用程式" : "下載Windows應用程式";
      getVersion({
        pcBundleId: os == "mac" ? "com.yiqimac.ArtVideoWB" : "com.msb.win32.1v1.student",
        pcBuildVersion: 0
      }).then(res => {
        if (res.data.status === 0) {
          this.url = res.data.upgrade.downurl;
        }
      });
    }
  },
  mounted() {
    this.version();
  }
};
</script>
<style lang="less" scoped>
.bg1 {
  background-image: url("@/assets/images/home/<USER>/download/banner背景图备份@2x.png");
  background-size: cover;
}
.btn_bg {
  background: linear-gradient(180deg, #ed92bd 0%, #792eb6 100%);
}
</style>
