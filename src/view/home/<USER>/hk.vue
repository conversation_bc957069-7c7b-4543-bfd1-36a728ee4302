<template>
  <div class="flex flex-col m-0 p-0">
    <nav-head></nav-head>
    <follow></follow>

    <div class="bg1 w-full h-[652px] pt-[136px]">
      <div class="w-[1200px] h-[470px] mx-auto flex justify-between items-center">
        <div class="">
          <div class="font-medium text-[38px] text-white leading-[53px]">網上學畫畫，就找美術寶</div>
          <div class="font-medium text-[38px] text-white leading-[53px]">線上美術私教</div>
          <div class="font-medium text-[38px] text-white leading-[53px]">專为4-18歲的兒童和青少年度身訂造</div>
          <div class="font-normal text-[20px] text-white leading-[28px] mt-[15px]">在家輕鬆學 ｜ 私教指導 ｜ 多語言教學</div>
        </div>

        <div class="w-[360px] h-[470px] rounded-[30px] bg-white pt-[28px] px-[55px]" id="register">
          <div class="text-center font-medium text-[20px] text-[#424242] leading-[28px]">- 歡迎預約試堂 -</div>
          <div class="text-center mt-[4px] font-normal text-[14px] text-[#B1B1B1] leading-[20px]">原價HK$215現新學員3折HK$65試堂</div>

          <form class="mt-[17px]">
            <input type="hidden" id="gclid_field" name="gclid_field" value="" />

            <div class="relative">
              <div class="relative w-[250px] h-[48px] bg-[#F4F4F4] rounded-[27px]">
                <area-code :isShow="showCode" :location="location" @changeCode="changeCode" @closePop="closePop"></area-code>
                <div class="flex justify-center items-center absolute left-0 top-0 w-[67px] cursor-pointer after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:w-[1px] after:h-[20px] after:bg-[#ededed] leading-[48px]" @click.stop="toggleCode">
                  <span class="text-[14px] inline-block relative pr-[12px] h-[48px] after:content-[''] after:absolute after:right-0 after:top-[22px] after:w-[8px] after:h-[6px] after:bg-[url('https://imgvip.meishubao.com/sea_web/img/form-more.png')] after:bg-center after:bg-no-repeat after:bg-[length:100%_100%] after:transition-all after:duration-300 after:origin-[center_30%]" :class="{ 'after:rotate-180': showCode }">{{ selectCode ? `+${selectCode}` : $t("请选择") }}</span>
                </div>
                <input type="text" v-model="phone" placeholder="電話" class="w-[200px] h-[48px] px-[20px] float-right" @blur="handleCheckPhone" @focus="handleClearPhone" />
                <div class="absolute text-[#FE8836] z-50 right-[20px] top-[14px]">必填</div>
              </div>
              <div class="error-tips" v-show="isPhone">*{{ phoneTips }}</div>
            </div>

            <div class="relative">
              <div class="mt-[17px] font-normal text-[14px] text-[#424242] leading-[20px]">請選擇您需要的服務語言（必填）</div>
              <div class="flex items-center">
                <input type="radio" id="language1" name="language" value="廣東話" v-model="language" @change="handleSelect" />
                <label class="ml-[7px] mr-[12px] font-normal text-[13px] text-[#424242] leading-[18px]" for="language1">廣東話</label>
                <input type="radio" id="language2" name="language" value="普通话" v-model="language" @change="handleSelect" />
                <label class="ml-[7px] mr-[12px] font-normal text-[13px] text-[#424242] leading-[18px]" for="language2">普通话</label>
                <input type="radio" id="language3" name="language" value="英語" v-model="language" @change="handleSelect" />
                <label class="ml-[7px] mr-[12px] font-normal text-[13px] text-[#424242] leading-[18px]" for="language3">英語</label>
              </div>
              <div class="error-tips" v-show="isLanguage">*{{ languageTips }}</div>
            </div>

            <div class="mt-[17px] relative w-[250px] h-[48px]">
              <input type="text" v-model="username" placeholder="姓名" class="w-[250px] h-[48px] rounded-[27px] bg-[#F4F4F4] px-[20px]" @blur="handleCheckName" @focus="handleClearName" />
              <div class="absolute z-50 right-[20px] top-[14px]">選填</div>
            </div>
            <!-- <div class="" v-show="isName">*{{ nameTips }}</div> -->
            <div class="relative">
              <div class="mt-[17px] relative w-[250px] h-[48px]">
                <input type="text" v-model="email" placeholder="電郵" class="w-[250px] h-[48px] rounded-[27px] bg-[#F4F4F4] px-[20px]" @blur="handleCheckEmail" @focus="handleClearEmail" />
                <div class="absolute z-50 right-[20px] top-[14px]">選填</div>
              </div>
              <!-- <div class="error-tips" v-show="isEmail">*{{ emailTips }}</div> -->
            </div>

            <div class="mt-[15px] flex items-center font-normal text-[12px] text-[#C3C3C3] leading-[17px]" @click="handleCheck">
              <img class="w-[10px] h-[10px] mr-[6px]" src="https://global-static.meishubao.com/sea_app/img/agreement-default.png" v-show="!isAgreement && !isEmpty" />
              <img class="w-[10px] h-[10px] mr-[6px]" src="https://global-static.meishubao.com/sea_app/img/agreement-checked.png" v-show="isAgreement" />
              <img class="w-[10px] h-[10px] mr-[6px]" src="https://global-static.meishubao.com/sea_web/img/no-check.png" v-show="!isAgreement & isEmpty" />
              <span class="">我已閱讀并同意<em class="text-[#4E98FF]" @click="handleAgreement">《用戶使用協議》</em><em class="text-[#4E98FF]" @click="handleSecret">《隱私協議》</em></span>
            </div>
            <div class="cursor-pointer anim_btn select-none mt-[26px] mx-auto btn_bg w-[160px] h-[48px] rounded-[27px] font-medium text-[15px] text-white flex items-center justify-center" @click="handleSave">登記試堂</div>
          </form>
        </div>
      </div>
    </div>

    <div class="bg2 w-full h-[700px] pt-[62px]">
      <div class="w-[1200px] h-[600px] rounded-[30px] bg-white mx-auto pt-[68px]">
        <div class="font-medium text-[32px] text-black leading-[45px] text-center">專業、客製化課堂</div>
        <div class="mt-[7px] font-normal text-[22px] text-[#A0A0A0] leading-[30px] text-center">讓您和孩子放心選擇</div>
        <div class="flex mt-[65px] flex-wrap justify-between">
          <div class="w-[600px] h-[100px] mb-[80px] flex pl-[70px]">
            <img class="w-[82px] h-[82px] mr-[30px]" src="@/assets/images/home/<USER>/移动端图标********" alt="" />
            <div class="font-normal text-[22px] text-black leading-[26px]">
              <div>師生1對1直播教學，面對面互動指導</div>
              <div class="mt-[14px] text-[#878787] text-[18px]">跟著老師看、聽、畫、高效學習</div>
              <div class="text-[#878787] text-[18px]">注意力集中不分心</div>
            </div>
          </div>
          <div class="w-[600px] h-[100px] mb-[80px] flex pl-[70px]">
            <img class="w-[82px] h-[82px] mr-[30px]" src="@/assets/images/home/<USER>/移动端图标********" alt="" flex />
            <div class="font-normal text-[22px] text-black leading-[26px]">
              <div>因材施教，個性化定製課程</div>
              <div class="mt-[14px] text-[#878787] text-[18px]">根據孩子的年齡、繪畫基礎、認知能力等</div>
              <div class="text-[#878787] text-[18px]">度身訂造適合孩子的繪畫過程</div>
            </div>
          </div>
          <div class="w-[600px] h-[100px] mb-[80px] flex pl-[70px]">
            <img class="w-[82px] h-[82px] mr-[30px]" src="@/assets/images/home/<USER>/移动端图标********" alt="" />
            <div class="font-normal text-[22px] text-black leading-[26px]">
              <div>安坐家中即可學習專業繪畫</div>
              <div class="mt-[14px] text-[#878787] text-[18px]">擁有專利攝像頭和AI透視技術</div>
              <div class="text-[#878787] text-[18px]">高度模擬真實畫班體驗</div>
            </div>
          </div>
          <div class="w-[600px] h-[100px] mb-[80px] flex pl-[70px]">
            <img class="w-[82px] h-[82px] mr-[30px]" src="@/assets/images/home/<USER>/移动端图标********" alt="" />
            <div class="font-normal text-[22px] text-black leading-[26px]">
              <div>家長可堂上旁觀，即時掌握學習動態</div>
              <div class="mt-[14px] text-[#878787] text-[18px]">教學過程全公開，家長可實時觀看授課過程，</div>
              <div class="text-[#878787] text-[18px]">見證孩子完整的創作歷程</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg3 w-full h-[700px]">
      <div class="w-[1200px] mx-auto pt-[66px] relative">
        <div class="font-medium text-[32px] text-black leading-[45px] text-center">數千名專業導師任你挑選</div>
        <div class="mt-[7px] font-normal text-[22px] text-[#A0A0A0] leading-[30px] text-center">5輪嚴選，教師錄取率僅3%</div>

        <div class="px-[50px] flex flex-wrap mt-[117px]">
          <div class="w-[550px] mb-[70px] flex justify-start">
            <div class="card_bg mr-[30px] flex items-center justify-center font-medium text-[14px] text-white w-[67px] h-[67px] rounded-[18px]">懂美術</div>
            <div class="w-[228px] font-normal text-[22px] text-black leading-[30px]">
              <div>每100位應徵老師中僅選出3位老師成為教員</div>
            </div>
          </div>
          <div class="w-[550px] mb-[70px] flex justify-end">
            <div class="card_bg mr-[30px] flex items-center justify-center font-medium text-[14px] text-white w-[67px] h-[67px] rounded-[18px]">懂溝通</div>
            <div class="w-[228px] font-normal text-[22px] text-black leading-[30px]">
              <div>不只是孩子的老師更是孩子的知心朋友</div>
            </div>
          </div>
          <div class="w-[550px] mb-[70px] flex justify-start">
            <div class="card_bg mr-[30px] flex items-center justify-center font-medium text-[14px] text-white w-[67px] h-[67px] rounded-[18px]">懂父母</div>
            <div class="w-[228px] font-normal text-[22px] text-black leading-[30px]">
              <div>了解你的痛點和焦慮有目標、有策略、有洞見的培養孩子</div>
            </div>
          </div>
          <div class="w-[550px] mb-[70px] flex justify-end">
            <div class="card_bg mr-[30px] flex items-center justify-center font-medium text-[14px] text-white w-[67px] h-[67px] rounded-[18px]">懂孩子</div>
            <div class="w-[228px] font-normal text-[22px] text-black leading-[30px]">
              <div>熟悉各年齡層孩子特質,尊重孩子個性,善於運用不同教學方法</div>
            </div>
          </div>
        </div>

        <img class="w-[288px] h-[482px] absolute left-[454px] top-[218px]" src="@/assets/images/home/<USER>/web/小模特图@2x.png" alt="" />
      </div>
    </div>

    <div class="bg-white w-full h-[650px]">
      <div class="w-[1200px] bg-white mx-auto pt-[80px]">
        <div class="font-medium text-[32px] text-black leading-[45px] text-center">隨時隨地Home School</div>
        <div class="mt-[7px] font-normal text-[22px] text-[#A0A0A0] leading-[30px] text-center">配備iPad或電腦即可上課</div>
        <div class="flex justify-around items-center mt-[84px]">
          <div class="flex flex-col justify-center items-center">
            <img class="mb-[38px] w-[120px] h-[120px]" src="@/assets/images/home/<USER>/移动端节省时间@2x.png" alt="" />
            <div class="mb-[26px] font-medium text-[28px] text-black leading-[24px]">節省時間</div>
            <div class="mb-[4px] font-medium text-[24px] text-[#828282] leading-[24px]">節省2小時交通接送時間</div>
            <div class="font-medium text-[24px] text-[#828282] leading-[24px]">孩子安全方便，家長放心安心</div>
          </div>
          <div class="flex flex-col justify-center items-center">
            <img class="mb-[38px] w-[120px] h-[120px]" src="@/assets/images/home/<USER>/移动端自由上课@2x.png" alt="" />
            <div class="mb-[26px] font-medium text-[28px] text-black leading-[24px]">彈性上課</div>
            <div class="mb-[4px] font-medium text-[24px] text-[#828282] leading-[24px]">上課時間你做主</div>
            <div class="font-medium text-[24px] text-[#828282] leading-[24px]">隨時隨地預約上課</div>
          </div>
          <div class="flex flex-col justify-center items-center">
            <img class="mb-[38px] w-[120px] h-[120px]" src="@/assets/images/home/<USER>/移动端价格实惠@2x.png" alt="" />
            <div class="mb-[26px] font-medium text-[28px] text-black leading-[24px]">價格實惠</div>
            <div class="mb-[4px] font-medium text-[24px] text-[#828282] leading-[24px]">比線下機構更優惠</div>
            <div class="font-medium text-[24px] text-[#828282] leading-[24px]">平均每課程節省超50%開支</div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-[#F8F8F8] w-full h-[950px]">
      <div class="w-[1200px] mx-auto pt-[80px]">
        <div class="font-medium text-[32px] text-black leading-[45px] text-center">匠心研發六階課程標準體系</div>
        <div class="mt-[7px] font-normal text-[22px] text-[#A0A0A0] leading-[30px] text-center">興趣啟蒙｜全面培養｜綜合提升</div>
        <img class="mt-[67px] mx-auto w-[1201px] h-[535px]" src="@/assets/images/home/<USER>/web/六阶课程体系@2x.png" alt="" />
        <div @click="$router.push('/system')" class="cursor-pointer select-none anim_btn w-[160px] h-[48px] rounded-[27px] bg-white flex justify-center items-center mt-[45px] mx-auto">
          <div class="font-medium text-[15px] text-[#7D7D7D] leading-[21px]">了解課程體系</div>
          <img class="w-[9px] h-[14px] ml-[10px]" src="@/assets/images/home/<USER>/web/灰色箭头@2x.png" alt="" />
        </div>
      </div>
    </div>

    <div class="bg-white w-full h-[800px]">
      <div class="w-[1200px] bg-white mx-auto pt-[80px] relative">
        <div class="font-medium text-[32px] text-black leading-[45px] text-center">為您與孩子提供貼心的教學服務</div>
        <div class="mt-[7px] font-normal text-[22px] text-[#A0A0A0] leading-[30px] text-center">課程策劃師 + 主科導師 + 學務顧問 + 教學管理主任</div>
        <div class="flex justify-between mt-[71px]">
          <div class="w-[344px]">
            <div class="mb-[27px] card_bg w-[103px] h-[38px] rounded-[19px] font-normal text-[18px] text-white leading-[25px] flex justify-center items-center">為孩子</div>
            <div class="font-normal text-[22px] text-black leading-[32px]">課前</div>
            <div class="font-normal text-[22px] text-[#A0A0A0] leading-[32px]">先評估後學習，從問題根源出發</div>
            <div class="mb-[32px] font-normal text-[22px] text-[#A0A0A0] leading-[32px]">與家長共同制定個人化學習計劃</div>
            <div class="font-normal text-[22px] text-black leading-[32px]">課中</div>
            <div class="font-normal text-[22px] text-[#A0A0A0] leading-[32px]">老師全程指導、講解評論、示範</div>
            <div class="mb-[32px] font-normal text-[22px] text-[#A0A0A0] leading-[32px]">每課時高頻率指導不少於30次</div>
            <div class="font-normal text-[22px] text-black leading-[32px]">課後</div>
            <div class="mb-[32px] font-normal text-[22px] text-[#A0A0A0] leading-[32px]">階段性成長報告，VIP群組1對1輔導對孩子作品進行精選、彙整、展覽</div>
          </div>
          <div class="w-[344px]">
            <div class="mb-[27px] card_bg w-[103px] h-[38px] rounded-[19px] font-normal text-[18px] text-white leading-[25px] flex justify-center items-center">為家長</div>
            <div class="font-normal text-[22px] text-black leading-[32px]">上課</div>
            <div class="font-normal text-[22px] text-[#A0A0A0] leading-[32px]">幫助您隨時約課、取消課程</div>
            <div class="mb-[32px] font-normal text-[22px] text-[#A0A0A0] leading-[32px]">上課設備的技術支持、更換老師等</div>
            <div class="font-normal text-[22px] text-black leading-[32px]">溝通</div>
            <div class="mb-[32px] font-normal text-[22px] text-[#A0A0A0] leading-[32px]">即時與您溝通、交流孩子學習狀況拆解孩子學習的每一步</div>
            <div class="font-normal text-[22px] text-black leading-[32px]">答疑</div>
            <div class="mb-[32px] font-normal text-[22px] text-[#A0A0A0] leading-[32px]">24小時為您解答疑問解決您的各種需求</div>
          </div>
        </div>

        <img class="w-[351px] h-[351px] absolute left-[425px] top-[294px]" src="@/assets/images/home/<USER>/web/少儿模特照片@2x.png" alt="" />
      </div>
    </div>

    <div class="bg-[#F7F7F7] w-full h-[971px]">
      <div class="w-[1200px] mx-auto pt-[74px]">
        <div class="font-medium text-[32px] text-black leading-[45px] text-center">學生作品展示</div>
        <div class="mt-[7px] font-normal text-[22px] text-[#A0A0A0] leading-[30px] text-center">孩子進步快，家長評價高</div>

        <div class="mt-[76px] flex flex-wrap justify-between">
          <div class="w-[251px] mb-[45px]">
            <img class="mb-[20px] w-[251px] h-[248px]" src="@/assets/images/home/<USER>/移动端作品********" alt="" />
            <div class="font-normal text-[16px] text-black leading-[22px]">《築夢未來》 李雨施 13歲</div>
          </div>
          <div class="w-[251px] mb-[45px]">
            <img class="mb-[20px] w-[251px] h-[248px]" src="@/assets/images/home/<USER>/移动端作品********" alt="" />
            <div class="font-normal text-[16px] text-black leading-[22px]">《和平之美》 陳思妤 6歲</div>
          </div>
          <div class="w-[251px] mb-[45px]">
            <img class="mb-[20px] w-[251px] h-[248px]" src="@/assets/images/home/<USER>/移动端作品********" alt="" />
            <div class="font-normal text-[16px] text-black leading-[22px]">《暢遊的魚群》 沙俊輝 10歲</div>
          </div>
          <div class="w-[251px] mb-[45px]">
            <img class="mb-[20px] w-[251px] h-[248px]" src="@/assets/images/home/<USER>/300-宗陈玮-11-青绿水松@2x.png" alt="" />
            <div class="font-normal text-[16px] text-black leading-[22px]">《青綠水松》 宗陳玮 11歲</div>
          </div>
          <div class="w-[251px] mb-[45px]">
            <img class="mb-[20px] w-[251px] h-[248px]" src="@/assets/images/home/<USER>/移动端作品********" alt="" />
            <div class="font-normal text-[16px] text-black leading-[22px]">《江南秋色》 馮梓瑜 10歲</div>
          </div>
          <div class="w-[251px] mb-[45px]">
            <img class="mb-[20px] w-[251px] h-[248px]" src="@/assets/images/home/<USER>/移动端作品********" alt="" />
            <div class="font-normal text-[16px] text-black leading-[22px]">《春醒》 周時羽 10歲</div>
          </div>
          <div class="w-[251px] mb-[45px]">
            <img class="mb-[20px] w-[251px] h-[248px]" src="@/assets/images/home/<USER>/移动端作品********" alt="" />
            <div class="font-normal text-[16px] text-black leading-[22px]">《火星之城》 胡凱爾 9歲</div>
          </div>
          <div class="w-[251px] mb-[45px]">
            <img class="mb-[20px] w-[251px] h-[248px]" src="@/assets/images/home/<USER>/移动端作品********" alt="" />
            <div class="font-normal text-[16px] text-black leading-[22px]">《天平秤》 彭潔微 10歲</div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg4 w-full h-[550px]">
      <div class="w-[1200px] mx-auto pt-[70px]">
        <div class="font-medium text-[32px] text-white leading-[45px] text-center">我們的承諾</div>
        <div class="mt-[7px] font-normal text-[22px] text-white leading-[30px] text-center">全心對學生，信心給承諾</div>

        <div class="mt-[83px] flex justify-between">
          <div class="w-[370px] h-[166px] rounded-[30px] bg-white flex flex-col justify-center items-center">
            <div class="mb-[16px] font-normal text-[24px] text-black leading-[28px]">試堂承諾</div>
            <div class="font-normal text-[18px] text-[#B5B5B5] leading-[28px]">首節課試聽</div>
            <div class="font-normal text-[18px] text-[#B5B5B5] leading-[28px]">滿意後再報名正式課</div>
          </div>

          <div class="w-[370px] h-[166px] rounded-[30px] bg-white flex flex-col justify-center items-center">
            <div class="mb-[16px] font-normal text-[24px] text-black leading-[28px]">隨時換老師承諾</div>
            <div class="font-normal text-[18px] text-[#B5B5B5] leading-[28px]">任何時候對老師不滿意</div>
            <div class="font-normal text-[18px] text-[#B5B5B5] leading-[28px]">均可申請更換老師</div>
          </div>
          <div class="w-[370px] h-[166px] rounded-[30px] bg-white flex flex-col justify-center items-center">
            <div class="mb-[16px] font-normal text-[24px] text-black leading-[28px]">無憂退款承諾</div>
            <div class="font-normal text-[18px] text-[#B5B5B5] leading-[28px]">課程效果不滿意</div>
            <div class="font-normal text-[18px] text-[#B5B5B5] leading-[28px]">隨時退還剩餘課時費</div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white w-full h-[200px]">
      <div class="w-[1200px] mx-auto pt-[58px] flex flex-col justify-center items-center">
        <div @click="goDownload" class="select-none cursor-pointer anim_btn card_bg w-[160px] h-[48px] rounded-[27px] font-medium text-[15px] text-white flex justify-center items-center">預約試堂</div>
        <div class="mt-[27px] text-center font-medium text-[16px] text-black leading-[22px]">如果你對Artworld美術寶的課程感興趣，可填寫登記試堂表格。我們的職員會盡快為您安排，並與您聯絡。</div>
      </div>
    </div>

    <nav-foot></nav-foot>

    <agreement-modal v-if="showAgreeModal" @closeAgreeModal="closeAgreeModal"></agreement-modal>
    <secret-modal v-if="showSecretModal" @closeAgreeModal="closeAgreeModal"></secret-modal>
  </div>
</template>
<script>
import PhoneNumberUtil from "google-libphonenumber";
const phoneUtil = PhoneNumberUtil.PhoneNumberUtil.getInstance();
import navHead from "@/components/headHk";
import follow from "@/components/follow";
import navFoot from "@/components/footHk";
import videoPlay from "@/components/videoPlay";
import agreementModal from "@/components/agreement";
import secretModal from "@/components/secret";
import areaCode from "@/components/areaCode";
import contact from "@/components/contact";

import { submitPhone, getIplLocation } from "@/api/api";
import codeData from "@/data/code.js";

import { utils, statistics } from "msb-public-library";
let reg = new RegExp("^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$");

import { t } from "i18n";
export default {
  name: "index",
  data() {
    return {
      teachArr: [
        {
          text1: t("懂父母"),
          text2: t("了解您的痛点和焦虑有目标,有策略,有反思的培养孩子"),
          showType: false
        },
        {
          text1: t("懂美术"),
          text2: t("每100位应聘老师中仅选出3位老师上岗教学"),
          showType: false
        },
        {
          text1: t("懂沟通"),
          text2: t("不仅是孩子的老师更是孩子的知心朋友"),
          showType: false
        },
        {
          text1: t("懂孩子"),
          text2: t("熟悉各个年龄段孩子特点,尊重孩子个性,善于运用不同教学方法"),
          showType: false
        }
      ],

      workArr: [
        {
          title: t("《丛林大冒险》"),
          name: t("王筱月"),
          age: t("11岁  上海"),
          longTime: t("在美术宝学习2年"),
          comment: t("“整体层次分明，色彩丰富细腻，奥、造型生动形象！”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work1.png",
          isSHow: false
        },
        {
          title: t("《我的动物朋友》"),
          name: "John",
          age: t("6岁  美国"),
          longTime: t("在美术宝学习半年"),
          comment: t("“点线面的构成感很棒，形象非常生动可爱。”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work2.png",
          isSHow: false
        },
        {
          title: t("《海边的夜晚》"),
          name: t("周子涵"),
          age: t("8岁  浙江杭州"),
          longTime: t("在美术宝学习了2年"),
          comment: t(" “画面丰富，用色大胆，构图上还有进步空间，加油！~”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work3.png",
          isShow: false
        },
        {
          title: t("《水彩柠檬》"),
          name: t("妮妮"),
          age: t("7岁  北京"),
          longTime: t("在美术宝学习2年"),
          comment: t(" “色彩亮丽，水彩水分把控的很好，色彩丰富程度可以再加强哦！”"),

          pic: "https://imgvip.meishubao.com/sea_web/img/work4.png",
          isShow: false
        },
        {
          title: t("《兔子宝宝》"),
          name: "Emily ",
          age: t("10岁  美国"),
          longTime: t("在美术宝学习1年"),
          comment: t("“小兔子的造型非常生动，毛发质感表现的也很好！”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work5.png",
          isSHow: false
        },
        {
          title: t("《森林之王》"),
          name: "Alex ",
          age: t("6岁"),
          longTime: t("在美术宝学习1年"),
          comment: t("宝贝用笔自信大胆，构图饱满，色彩搭配很棒哦！”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work6.png",
          isSHow: false
        }
      ],

      curOver: false,
      videoWidth: 0,
      videoHeight: 0,
      bannerHeight: 840,
      playing: false,
      selectCode: "852",
      selectContact: t("微信"),
      phone: "",
      email: "",
      username: "",
      isName: false,
      isEmail: false,
      isPhone: false,
      isLanguage: false,
      nameTips: "",
      emailTips: "",
      languageTips: "",
      phoneTips: "",
      isAgreement: true,
      isEmpty: false,
      showAgreeModal: false,
      showSecretModal: false,
      location: 52,
      showCode: false,
      showContact: false,
      adplatform: "",
      language: ""
    };
  },
  watch: {
    showAgreeModal(val) {
      if (val) {
        document.body.setAttribute("style", `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`);
      } else {
        document.body.removeAttribute("style");
      }
    },
    showSecretModal(val) {
      if (val) {
        document.body.setAttribute("style", `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`);
      } else {
        document.body.removeAttribute("style");
      }
    },
    playing(val) {
      if (val) {
        document.body.setAttribute("style", `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`);
      } else {
        document.body.removeAttribute("style");
      }
    },
    adplatform(val) {
      if (val == 12) {
        this.linkinStatics();
      }
      if (val == 5 || val == 6) {
        this.getStaticsFnc()[this.getStaticsType(this.adplatform)].init();
        window.reportAd = this.getStaticsFnc()[this.getStaticsType(this.adplatform)].success;
      }
    }
  },
  mounted() {
    let clientWidth = document.documentElement.clientWidth;

    //sessionStorage 防止切换页面，投放首页的参数丢失
    const platform = sessionStorage.getItem("adplatform");
    this.adplatform = this.$route.query.adplatform || platform || "";
    sessionStorage.setItem("adplatform", this.adplatform);
    this.getIp();
    window.addEventListener("scroll", this.handleScroll);
    let channelid = this.$route.query.channel;
    sessionStorage.setItem("channel", channelid);
  },
  methods: {
    goDownload() {
      this.$router.push(`/#register?t=${new Date().getTime()}`);
    },
    async getIp() {
      try {
        let res = await getIplLocation();
        if (res.data && res.data.status === 0) {
          const { data } = res.data;
          const city = data.country;
          codeData.forEach(areaItem => {
            if (areaItem.zh === city) {
              // this.selectCode = areaItem.code;
            }
          });
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleOver(i) {
      let obj = this.teachArr[i];
      this.$set(obj, "showType", true);
      this.curOver = true;
    },
    handleOut(i) {
      let obj = this.teachArr[i];
      this.$set(obj, "showType", false);
      this.curOver = false;
    },
    handleWorkover(i) {
      let obj = this.workArr[i];
      this.$set(obj, "isShow", true);
    },
    handleWorkout(i) {
      let obj = this.workArr[i];
      this.$set(obj, "isShow", false);
    },
    handlePlayVideo() {
      this.playing = true;
    },
    //动态设置id
    getId(index) {
      return "pos" + (index + 1);
    },
    //教师介绍
    handleTeacher() {
      this.$router.push({
        path: "/teachIntroduction"
      });
    },
    //课程体系
    handleCourse() {
      this.$router.push({
        path: "/setCourse"
      });
    },
    closevideo(e) {
      this.playing = e;
    },
    closeAgreeModal() {
      this.showAgreeModal = false;
      this.showSecretModal = false;
    },
    handleSelect(e) {
      this.languageTips = "";
      this.isLanguage = false;
      this.language = e.target.value;
    },
    //选择用户协议
    handleCheck() {
      this.isAgreement = !this.isAgreement;
    },
    // 验证手机号
    handleCheckPhone() {
      if (this.phone === "") {
        this.phoneTips = t("手机号不能为空");
        this.isPhone = true;
      } else if (this.phone.length < 1) {
        this.phoneTips = t("手机号格式不正确，请重新输入");
        this.isPhone = true;
      }
    },
    // 验证邮箱
    handleCheckEmail() {
      if (this.email === "") {
        this.emailTips = t("联系方式不能为空");
        this.isEmail = true;
      } else if (this.selectContact === t("邮箱")) {
        if (!reg.test(this.email)) {
          this.emailTips = t("邮箱格式有误");
          this.isEmail = true;
          returnFlag = true;
        }
      }
    },
    handleCheckName() {
      if (this.username === "") {
        this.nameTips = t("姓名不能为空");
        this.isName = true;
      }
    },
    handleClearPhone() {
      this.phoneTips = "";
      this.isPhone = false;
    },
    handleClearEmail() {
      this.emailTips = "";
      this.isEmail = false;
    },
    handleClearName() {
      this.nameTips = "";
      this.isName = false;
    },
    //保存注册信息
    handleSave() {
      let returnFlag = false;
      // if (this.username === "") {
      //   this.nameTips = t("姓名不能为空");
      //   this.isName = true;
      //   returnFlag = true;
      // }
      if (this.selectCode === "") {
        this.phoneTips = t("请选择区号");
        this.isPhone = true;
        returnFlag = true;
      } else if (this.phone === "") {
        this.phoneTips = t("手机号不能为空");
        this.isPhone = true;
        returnFlag = true;
      } else if (this.phone.length < 1) {
        this.phoneTips = t("手机号格式不正确，请重新输入");
        this.isPhone = true;
        returnFlag = true;
      } else {
        this.isPhone = false;
      }
      if (!this.language) {
        this.languageTips = "请选择语言";
        this.isLanguage = true;
        returnFlag = true;
      }
      // if (this.email === "") {
      //   this.emailTips = t("联系方式不能为空");
      //   this.isEmail = true;
      //   returnFlag = true;
      // } else if (this.selectContact === t("邮箱")) {
      // if (!reg.test(this.email)) {
      //   this.emailTips = t("邮箱格式有误");
      //   this.isEmail = true;
      //   returnFlag = true;
      // }
      // } else {
      //   this.isEmail = false;
      // }
      if (!this.isAgreement) {
        this.isEmpty = true;
        setTimeout(() => {
          this.isEmpty = false;
        }, 500);
        returnFlag = true;
      }
      if (returnFlag) return false;
      let params = {
        is_join: 1,
        channel: this.$route.query.channel || sessionStorage.getItem("channel") || import.meta.env.VITE_APP_HK_CHANNEL,
        adplatform: this.$route.query.adplatform || "",
        bd_vid: this.$route.query.bd_vid || "",
        mcode: this.selectCode.includes("+") ? this.selectCode : `+${this.selectCode}`,
        language: this.language,
        mobile: this.phone,
        name: this.username,
        orther: `${this.selectContact}：${this.email}`
      };
      this.getLinkin();
      submitPhone(params).then(res => {
        if (res.data.code === 200) {
          if (res.data.is_first) {
            if (this.adplatform == 5 || this.adplatform == 6) {
              this.getStaticsFnc()[this.getStaticsType(this.adplatform)].success({
                email: this.selectContact == t("邮箱") ? this.email : "",
                phone_number: this.phone
              });
            }
            this.$toast("註冊成功，請耐心等待服務人員聯繫。");
          } else {
            this.$toast(res.data.msg);
          }
        } else {
          this.$toast(res.data.msg);
        }
      });
    },
    changeCode(val) {
      this.selectCode = String(val);
      this.showCode = false;
    },
    changeContact(val) {
      this.selectContact = val;
      this.showContact = false;
    },
    closePop() {
      this.showCode = false;
    },
    closePopContact() {
      this.showContact = false;
    },
    toggleCode(e) {
      this.closePopContact();
      this.showCode = !this.showCode;
      this.location = e.target.getBoundingClientRect().height + 2;
    },
    toggleContact(e) {
      this.closePop();
      this.showContact = !this.showContact;
      this.location = e.target.getBoundingClientRect().height + 2;
    },
    handleAgreement() {
      this.showAgreeModal = true;
    },
    handleSecret() {
      this.showSecretModal = true;
    },
    hideTip() {
      this.isName = false;
      this.isPhone = false;
      this.isEmail = false;
    },
    linkinStatics() {
      var _linkedin_partner_id = "2462412";
      window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
      window._linkedin_data_partner_ids.push(_linkedin_partner_id);
      utils.loadScript(`https://snap.licdn.com/li.lms-analytics/insight.min.js`);
    },
    getLinkin() {
      const img = document.createElement("img");
      img.src = "https://px.ads.linkedin.com/collect/?pid=2462412&conversionId=2917505&fmt=gif";
      img.style = "display:none";
      img.width = 1;
      img.height = 1;
      document.body.appendChild(img);
    },
    getStaticsType(param) {
      let type = "";
      switch (param) {
        case "5":
          type = "google";
          break;
        case "6":
          type = "facebook";
          break;
        default:
          type = "";
          break;
      }
      return type;
    },
    gtag() {
      window.dataLayer.push(arguments);
    },
    googleGTM() {
      utils.loadScript("//cdn.ampproject.org/v0/amp-analytics-0.1.js", {
        "custom-element": "amp-analytics"
      });
      const _dom = document.createElement("amp-analytics");
      _dom.setAttribute("config", "https://www.googletagmanager.com/amp.json?id=GTM-T4LZLD3&gtm.url=SOURCE_URL");
      _dom.setAttribute("data-credentials", "include");
      document.body.insertBefore(_dom, document.body.firstChild);
    },
    getStaticsFnc() {
      let obj = {
        google: {
          init: () => {
            this.googleGTM();
            const { gg_id = "", gg_key = "" } = this.$route.query;
            sessionStorage.setItem("adInfo", JSON.stringify({ gg_id, gg_key }));
            console.log("google init", gg_id, gg_key);
            utils.loadScript(`//www.googletagmanager.com/gtag/js?id=UA-155116736-1`);
            utils.loadScript(`//www.googletagmanager.com/gtag/js?id=${gg_id}`);
            window.dataLayer = window.dataLayer || [];
            this.gtag("js", new Date());
            // ga统计
            this.gtag("config", "UA-155116736-1");
            this.gtag("config", gg_id);
          },
          success: ({ url, email, phone_number }) => {
            const adInfo = JSON.parse(sessionStorage.getItem("adInfo")) || {};
            const { gg_id = "", gg_key = "" } = adInfo;
            const callback = () => {
              if (typeof url != "undefined") {
                window.location = url;
              }
            };

            this.gtag("event", "conversion", {
              send_to: `${gg_id}/${gg_key}`,
              value: 1.0,
              currency: "USD",
              event_callback: callback
            });
            this.gtag("event", "receive_success", {
              event_category: "course"
            });
            this.gtag("event", "leadform", {
              user_data: {
                email,
                phone_number
              }
            });
          }
        },
        facebook: {
          init: () => {
            const { fb_id } = this.$route.query;
            console.log("facebook init", fb_id);
            !(function (f, b, e, v, n, t, s) {
              if (f.fbq) return;
              n = f.fbq = function () {
                n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
              };
              if (!f._fbq) f._fbq = n;
              n.push = n;
              n.loaded = !0;
              n.version = "2.0";
              n.queue = [];
              t = b.createElement(e);
              t.async = !0;
              t.src = v;
              s = b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t, s);
            })(window, document, "script", "https://connect.facebook.net/en_US/fbevents.js");
            window.fbq("init", fb_id);
            window.fbq("track", "PageView");
          },
          success: () => {
            window.fbq("track", "CompleteRegistration");
          }
        }
      };
      return obj;
    }
  },
  components: {
    navHead,
    follow,
    navFoot,
    videoPlay,
    agreementModal,
    secretModal,
    areaCode,
    contact
  },
  metaInfo: {
    title: "Artworld美術寶-在線少兒美術學畫畫平台-線上美術私教-網上學畫畫",
    meta: [
      {
        name: "keywords",
        content: "美術寶HongKong,美術寶,美術寶1對1,美術寶1on1,美術寶一對一,少兒美術,在線少兒美術,兒童美術,在線兒童美術,美術寶Global,美術寶1對1香港官網"
      },
      {
        name: "description",
        content: "Artworld美術寶是擁有數千名美院專業教師的在線兒童美術畫畫平臺！致力於為每個家庭提供個性化定製的美育課程，主要面向4-18歲適齡兒童，采用1對1真人在線互動教學形式，利用iPad、電腦等終端，隨時隨地的提供少兒藝術培養專屬課程。"
      }
    ]
  }
};
</script>
<style lang="less" scoped>
.bg1 {
  background-image: url("@/assets/images/home/<USER>/web/官网首页背景图********");
  background-size: cover;
}
.bg2 {
  background-image: url("@/assets/images/home/<USER>/web/官网首页背景图********");
  background-size: cover;
}
.bg3 {
  background-image: url("@/assets/images/home/<USER>/web/官网首页图********");
  background-size: cover;
}
.bg4 {
  background-image: url("@/assets/images/home/<USER>/web/我们的承诺背景图@2x.png");
  background-size: cover;
}
.btn_bg {
  background: linear-gradient(180deg, #ed92bd 0%, #792eb6 100%);
}
.card_bg {
  background: linear-gradient(180deg, #ed92bd 0%, #792eb6 100%);
}

.error-tips {
  top: 4px;
  right: 260px;
  padding-left: 37px;
  &::after {
    position: absolute;
    right: -16px;
    top: 10px;
    display: block;
    content: "";
    width: 0;
    height: 0;
    border-style: solid;
    border-top: 10px transparent dashed;
    border-right: 10px transparent dashed;
    border-bottom: 10px transparent dashed;
    border-left: 10px #ff5938 solid;
    border-radius: 2px;
  }
}
</style>
