<template>
  <div class="sea-index">
    <div class="to-hk" v-if="isHK">
      <span>是否跳轉香港本地網站瀏覽？</span>
      <a :href="`https://${VITE_APP_HK_HOST}`" class="sea-link">點擊前往</a>
    </div>
    <nav-head :isHK="isHK"></nav-head>
    <div class="sea-banner">
      <div class="sea-banner-content">
        <div class="sea-video-banner">
          <div class="mask"></div>
          <video
            data-v-3a6b570a=""
            id="index_video"
            width="100%"
            height="100%"
            loop="loop"
            autoplay="autoplay"
            muted="muted"
            poster="https://imgvip.meishubao.com/sea_web/img/banner-bg.jpg"
          >
            <source
              data-v-3a6b570a=""
              src="https://imgvip.meishubao.com/sea_web/video/index_video.mp4"
            />
          </video>
        </div>
        <div class="sea-video-text">
          <!-- <img
            src="https://imgvip.meishubao.com/sea_web/img/tengxun-info.png"
            class="sea-banner-info1"
          /> -->
          <div class="sea-banner-info2">
            <p>{{ $t("在线学画画") }}</p>
            <p>{{ $t("就找美术宝1对1") }}</p>
          </div>
          <p class="sea-banner-age">{{ $t("为4-18岁孩子量身定制") }}</p>
          <div class="sea-banner-tags clearfix">
            <span class="tags fl">{{ $t("美院师资") }}</span>
            <span class="tags fl">{{ $t("在家学习") }}</span>
            <span class="tags fl">{{ $t("数亿元融资") }}</span>
          </div>
        </div>
        <div class="sea-register" v-clickoutside="hideTip">
          <h3 class="title">
            <em>{{ $t("免费") }}</em
            >{{ $t("领取") }}<em>{{ $t("0元") }}</em
            >{{ $t("试听课") }}
          </h3>
          <!-- <p class="desc">限领<em>1</em>次，每日限<em>30</em>个免费名额</p> -->
          <form class="register-form">
            <div class="form-item form-name">
              <input
                type="text"
                class="name-input"
                v-model="username"
                :placeholder="$t('请填写您的姓名')"
                @focus="handleClearName"
                @blur="handleCheckName"
              />
              <div class="error-tips name-tips" v-if="isName">
                <span>{{ nameTips }}</span>
              </div>
            </div>
            <input type="hidden" id="gclid_field" name="gclid_field" value="" />
            <div class="form-item form-tel clearfix">
              <area-code
                :isShow="showCode"
                :location="location"
                @changeCode="changeCode"
                @closePop="closePop"
              ></area-code>
              <div class="tel-code fl" @click.stop="toggleCode">
                <span>{{ selectCode ? `+${selectCode}` : $t("请选择") }}</span>
              </div>
              <input
                type="text"
                v-model="phone"
                class="tel-input fl"
                :placeholder="$t('请输入手机号')"
                @focus="handleClearPhone"
                @blur="handleCheckPhone"
              />
              <div class="error-tips" v-if="isPhone">
                <span>{{ phoneTips }}</span>
              </div>
            </div>
            <div class="form-item form-email clearfix">
              <contact
                :isShow="showContact"
                :location="location"
                @changeCode="changeContact"
                @closePop="closePopContact"
              ></contact>
              <div class="tel-contact fl" @click.stop="toggleContact">
                <span>{{
                  selectContact ? `${selectContact}` : $t("请选择")
                }}</span>
              </div>
              <input
                type="text"
                class="email-input"
                v-model="email"
                :placeholder="
                  selectContact === ''
                    ? $t('请输入联系方式')
                    : $t('请输入selectContact', {
                        selectContact: selectContact
                      })
                "
                @focus="handleClearEmail"
                @blur="handleCheckEmail"
              />
              <div class="error-tips email-tips" v-if="isEmail">
                <span>{{ emailTips }}</span>
              </div>
            </div>
            <div class="form-agreement clearfix">
              <span class="check-btn fl" @click="handleCheck">
                <img
                  src="https://imgvip.meishubao.com/sea_web/img/check-default.png"
                  v-show="!isAgreement && !isEmpty"
                />
                <img
                  src="https://imgvip.meishubao.com/sea_web/img/check-select.png"
                  v-show="isAgreement"
                />
                <img
                  src="https://imgvip.meishubao.com/sea_web/img/no-check.png"
                  class="check-error"
                  v-show="!isAgreement && isEmpty"
                />
              </span>
              <a class="agreement-text fl"
                >{{ $t("我已阅读并同意")
                }}<em @click="handleAgreement">{{ $t("《用户协议》") }}</em
                >{{ $t("和")
                }}<em @click="handleSecret">{{ $t("《隐私协议》") }}</em></a
              >
            </div>
            <div class="form-item form-save" @click="handleSave">
              {{ $t("立即领取") }}
            </div>
          </form>
          <div class="register-have">
            <h3 class="tit">
              <img
                src="https://imgvip.meishubao.com/sea_web/img/left-line.png"
                class="img-l"
              />{{ $t("孩子将获得")
              }}<img
                src="https://imgvip.meishubao.com/sea_web/img/right-line.png"
                class="img-r"
              />
            </h3>
            <div class="register-have-item clearfix">
              <a href="#" class="have-item-list fl">
                <img
                  src="https://imgvip.meishubao.com/sea_web/img/register1.png"
                  class="img-l"
                />
                <span class="text">{{ $t("1对1直播课") }}</span>
              </a>
              <a href="#" class="have-item-list fr">
                <img
                  src="https://imgvip.meishubao.com/sea_web/img/register2.png"
                  class="img-l"
                />
                <span class="text">{{ $t("美术能力测评") }}</span>
              </a>
              <a href="#" class="have-item-list fl">
                <img
                  src="https://imgvip.meishubao.com/sea_web/img/register3.png"
                  class="img-l"
                />
                <span class="text">{{ $t("美术学习方案") }}</span>
              </a>
              <a href="#" class="have-item-list fr">
                <img
                  src="https://imgvip.meishubao.com/sea_web/img/register4.png"
                  class="img-l"
                />
                <span class="text">{{ $t("上课实体设备") }}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sea-video">
      <h3 class="sea-item-title">{{ $t("让孩子开心画画 快乐成长") }}</h3>
      <p class="sea-item-desc">{{ $t("用孩子喜欢的方式学画画") }}</p>
      <div class="sea-main sea-video-content clearfix">
        <ul class="sea-video-l fl">
          <li class="clearfix">
            <img
              src="https://imgvip.meishubao.com/sea_web/img/home1-icon.png"
              class="home-icon fl"
            />
            <div class="home-desc fl">
              <h3 class="home-desc-tit">
                {{ $t("师生面对面，1对1直播互动") }}
              </h3>
              <div class="home-desc-info">
                <p>{{ $t("跟着老师看、听、画、高效学习") }}</p>
                <p>{{ $t("注意力集中不走神") }}</p>
              </div>
            </div>
          </li>
          <li class="clearfix">
            <img
              src="https://imgvip.meishubao.com/sea_web/img/home2-icon.png"
              class="home-icon fl"
            />
            <div class="home-desc fl">
              <h3 class="home-desc-tit">
                {{ $t("足不出户，在家就能学画画") }}
              </h3>
              <div class="home-desc-info">
                <p>{{ $t("拥有专利摄像头和AI透视算法") }}</p>
                <p>{{ $t("真实还原线下授课场景") }}</p>
              </div>
            </div>
          </li>
          <li class="clearfix">
            <img
              src="https://imgvip.meishubao.com/sea_web/img/home3-icon.png"
              class="home-icon fl"
            />
            <div class="home-desc fl">
              <h3 class="home-desc-tit">
                {{ $t("因材施教，个性化定制课程") }}
              </h3>
              <div class="home-desc-info">
                <p>{{ $t("根据孩子的年龄、绘画基础、认知等") }}</p>
                <p>{{ $t("量身定制适合孩子学习的课程") }}</p>
              </div>
            </div>
          </li>
          <li class="clearfix">
            <img
              src="https://imgvip.meishubao.com/sea_web/img/home4-icon.png"
              class="home-icon fl"
            />
            <div class="home-desc fl">
              <h3 class="home-desc-tit">
                {{ $t("家长可旁听，实时掌握学习动态") }}
              </h3>
              <div class="home-desc-info">
                <p>{{ $t("教学透明，家长可观看整个授课过程") }}</p>
                <p>{{ $t("见证孩子完整创作历程") }}</p>
              </div>
            </div>
          </li>
        </ul>
        <div class="sea-video-r fl">
          <img
            src="https://imgvip.meishubao.com/sea_web/img/poster_video1.png"
            class="poster"
          />
          <img
            src="https://imgvip.meishubao.com/sea_web/img/play-icon.png"
            class="play-icon"
            @click="handlePlayVideo"
          />
        </div>
      </div>
    </div>
    <div class="sea-teacher">
      <h3 class="sea-item-title">{{ $t("数万名专业名师任您挑选") }}</h3>
      <p class="sea-item-desc">{{ $t("5轮严选，教师录取率仅3%") }}</p>
      <div class="sea-main sea-teacher-content" :class="{ hover: curOver }">
        <div
          v-for="(item, i) in teachArr"
          :key="i"
          class="advantage-item"
          :id="getId(i)"
          @mouseover="handleOver(i)"
          @mouseout="handleOut(i)"
        >
          <p v-show="!item.showType">{{ item.text1 }}</p>
          <p v-show="item.showType" class="small-text">{{ item.text2 }}</p>
        </div>
        <div class="sea-teacher-circle circle1"></div>
        <div class="sea-teacher-circle circle2"></div>
        <div class="sea-teacher-circle circle3"></div>
        <div class="sea-teacher-circle circle4"></div>
      </div>
      <img
        src="https://imgvip.meishubao.com/sea_web/img/teacher.png?v=20200731"
        class="sea-teacher-img"
      />
      <div class="sea-button sea-dev-button" @click="handleTeacher">
        {{ $t("更多老师介绍") }}
      </div>
      <img
        src="https://imgvip.meishubao.com/sea_web/img/teacher-bg.png"
        class="bottom-bg"
      />
    </div>
    <div class="sea-ready">
      <h3 class="sea-item-title">{{ $t("随时在家就能学") }}</h3>
      <p class="sea-item-desc">{{ $t("有iPad或者电脑就能上课") }}</p>
      <div class="sea-main sea-ready-content clearfix">
        <div class="sea-ready-item">
          <img src="https://imgvip.meishubao.com/sea_web/img/ready-time.png" />
          <h3 class="ready-item-title">{{ $t("节省时间") }}</h3>
          <div class="ready-item-desc">
            <p>{{ $t("省下2小时路上接送时间") }}</p>
            <p>{{ $t("孩子安全方便，家长放心省心") }}</p>
          </div>
        </div>
        <div class="sea-ready-item">
          <img src="https://imgvip.meishubao.com/sea_web/img/ready-free.png" />
          <h3 class="ready-item-title">{{ $t("自由上课") }}</h3>
          <div class="ready-item-desc">
            <p>{{ $t("上课时间你做主") }}</p>
            <p>{{ $t("随时随地预约上课") }}</p>
          </div>
        </div>
        <div class="sea-ready-item sea-ready-last">
          <img src="https://imgvip.meishubao.com/sea_web/img/ready-price.png" />
          <h3 class="ready-item-title">{{ $t("价格实惠") }}</h3>
          <div class="ready-item-desc">
            <p>{{ $t("比线下机构更优惠") }}</p>
            <p>{{ $t("平均每课时节省超50%费用") }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="sea-main sea-development">
      <h3 class="sea-item-title">{{ $t("匠心研发6阶课程标准体系") }}</h3>
      <p class="sea-item-desc">{{ $t("兴趣启蒙 全面培养 综合提升") }}</p>
      <div class="sea-main sea-development-content">
        <img
          src="https://imgvip.meishubao.com/sea_web/img/dev-course.png?v=20200801"
        />
      </div>
      <div class="sea-button sea-dev-button" @click="handleCourse">
        {{ $t("了解课程体系") }}
      </div>
    </div>
    <div class="sea-service">
      <h3 class="sea-item-title">{{ $t("为您和孩子提供贴心的教学服务") }}</h3>
      <p class="sea-item-desc">
        {{ $t("课程规划师+任课教师+课程顾问+教学管理老师") }}
      </p>
      <p class="sea-item-desc">{{ $t("4位老师同时服务于1名学员") }}</p>
      <div class="sea-service-content sea-main">
        <div class="service-item-l">
          <div class="item-tags">
            <h3 class="tags child">{{ $t("为孩子") }}</h3>
          </div>
          <div class="item-list">
            <h3 class="item-list-title">{{ $t("课前") }}</h3>
            <div class="item-list-desc">
              <p>{{ $t("先诊断后学习，从问题出发") }}</p>
              <p>{{ $t("和家长一起制定学习计划") }}</p>
            </div>
          </div>
          <div class="item-list">
            <h3 class="item-list-title">{{ $t("课中") }}</h3>
            <div class="item-list-desc">
              <p>{{ $t("老师全程指点、讲评、示范") }}</p>
              <p>{{ $t("每课时高精度指导不少于30次") }}</p>
            </div>
          </div>
          <div class="item-list">
            <h3 class="item-list-title">{{ $t("课后") }}</h3>
            <div class="item-list-desc">
              <p>{{ $t("阶段性成长报告，VIP群1对1辅导") }}</p>
              <p>{{ $t("对孩子作品进行精选、汇总、展览") }}</p>
            </div>
          </div>
        </div>
        <div class="service-item-c">
          <img
            src="https://imgvip.meishubao.com/sea_web/img/service-bg.png?v=20200731"
          />
        </div>
        <div class="service-item-l service-item-r">
          <div class="item-tags">
            <h3 class="tags">{{ $t("为父母") }}</h3>
          </div>
          <div class="item-list">
            <h3 class="item-list-title">{{ $t("上课") }}</h3>
            <div class="item-list-desc">
              <p>{{ $t("帮助您随时约课、取消课程") }}</p>
              <p>{{ $t("调试上课设备、更换老师等") }}</p>
            </div>
          </div>
          <div class="item-list">
            <h3 class="item-list-title">{{ $t("沟通") }}</h3>
            <div class="item-list-desc">
              <p>{{ $t("实时与您沟通、交流孩子学习状况") }}</p>
              <p>{{ $t("拆解孩子学习的每一步") }}</p>
            </div>
          </div>
          <div class="item-list">
            <h3 class="item-list-title">{{ $t("答疑") }}</h3>
            <div class="item-list-desc">
              <p>{{ $t("24小时为您答疑解惑") }}</p>
              <p>{{ $t("解决您的各种问题") }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sea-work">
      <h3 class="sea-item-title">{{ $t("学生作品展示") }}</h3>
      <p class="sea-item-desc">{{ $t("孩子进步快，家长评价高") }}</p>
      <div class="sea-main sea-work-content clearfix">
        <div
          class="sea-work-item fl"
          v-for="(item, i) in workArr"
          :key="i"
          :class="{ 'work-last': (i + 1) % 3 === 0 }"
          @mouseover="handleWorkover(i)"
          @mouseout="handleWorkout(i)"
        >
          <img class="imgs" :src="item.pic" />
          <div v-show="item.isShow" class="work-item-student">
            <p class="work-info">{{ item.title }}</p>
            <p class="work-info">{{ item.name }}</p>
            <p class="work-info1">{{ item.age }}</p>
            <p class="work-info1">{{ item.longTime }}</p>
            <img
              src="https://imgvip.meishubao.com/sea_web/img/work-line.png"
              class="work-line"
            />
            <p class="work-info2">{{ $t("老师评语：") }}</p>
            <p class="work-info2">{{ item.comment }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="sea-medio">
      <h3 class="sea-item-title">{{ $t("媒体报道") }}</h3>
      <div class="sea-main sea-medio-content">
        <img src="https://imgvip.meishubao.com/sea_web/img/medio.png" />
      </div>
    </div>
    <div class="sea-investment">
      <h3 class="sea-item-title">{{ $t("实力保证 资本青睐") }}</h3>
      <p class="sea-item-desc">{{ $t("腾讯投资企业 累计获得超20亿元融资") }}</p>
      <p class="sea-item-desc">{{ $t("80%用户严选师资+课程研发") }}</p>
      <div class="sea-main sea-investment-content">
        <img src="https://imgvip.meishubao.com/sea_web/img/investment-pc.png" />
      </div>
    </div>
    <div class="sea-promise">
      <h3 class="sea-item-title">{{ $t("我们的承诺") }}</h3>
      <p class="sea-item-desc">{{ $t("全新对学生，信心给承诺") }}</p>
      <div class="sea-main sea-promise-content clearfix">
        <div class="promise-item fl clearfix">
          <img
            src="https://imgvip.meishubao.com/sea_web/img/listen-icon.png"
            class="promise-item-img fl"
          />
          <div class="promise-item-text fl">
            <h3 class="title">{{ $t("免费试听承诺") }}</h3>
            <div class="desc">
              <p>{{ $t("首节课免费试听") }}</p>
              <p>{{ $t("满意后再付费上课") }}</p>
            </div>
          </div>
        </div>
        <div class="promise-item fl clearfix">
          <img
            src="https://imgvip.meishubao.com/sea_web/img/teach-icon.png"
            class="promise-item-img1 fl"
          />
          <div class="promise-item-text fl">
            <h3 class="title">{{ $t("随时换老师承诺") }}</h3>
            <div class="desc">
              <p>{{ $t("任何时候对老师不满意") }}</p>
              <p>{{ $t("均可申请更换老师") }}</p>
            </div>
          </div>
        </div>
        <div class="promise-item child-last fl clearfix">
          <img
            src="https://imgvip.meishubao.com/sea_web/img/money-icon.png"
            class="promise-item-img2 fl"
          />
          <div class="promise-item-text fl">
            <h3 class="title">{{ $t("无忧退款承诺") }}</h3>
            <div class="desc">
              <p>{{ $t("课程效果不满意") }}</p>
              <p>{{ $t("120天内退还剩余课时费") }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <nav-foot page="index"></nav-foot>
    <video-play
      v-if="playing"
      @closed="closevideo"
      videoSrc="https://imgvip.meishubao.com/sea_web/video/index_video1.mp4"
    ></video-play>
    <agreement-modal
      v-if="showAgreeModal"
      @closeAgreeModal="closeAgreeModal"
    ></agreement-modal>
    <secret-modal
      v-if="showSecretModal"
      @closeAgreeModal="closeAgreeModal"
    ></secret-modal>
  </div>
</template>
<script>
import PhoneNumberUtil from "google-libphonenumber";
const phoneUtil = PhoneNumberUtil.PhoneNumberUtil.getInstance();
import navHead from "@/components/head";
import navFoot from "@/components/foot";
import videoPlay from "@/components/videoPlay";
import agreementModal from "@/components/agreement";
import secretModal from "@/components/secret";
import areaCode from "@/components/areaCode";
import contact from "@/components/contact";

import { submitPhone, getIplLocation } from "@/api/api";
import codeData from "@/data/code.js";

import { utils, statistics } from "msb-public-library";
let reg = new RegExp(
  "^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$"
);
import { t } from "i18n";
export default {
  name: "index",
  data() {
    return {
      teachArr: [
        {
          text1: t("懂父母"),
          text2: t("了解您的痛点和焦虑有目标,有策略,有反思的培养孩子"),
          showType: false
        },
        {
          text1: t("懂美术"),
          text2: t("每100位应聘老师中仅选出3位老师上岗教学"),
          showType: false
        },
        {
          text1: t("懂沟通"),
          text2: t("不仅是孩子的老师更是孩子的知心朋友"),
          showType: false
        },
        {
          text1: t("懂孩子"),
          text2: t("熟悉各个年龄段孩子特点,尊重孩子个性,善于运用不同教学方法"),
          showType: false
        }
      ],

      workArr: [
        {
          title: t("《丛林大冒险》"),
          name: t("王筱月"),
          age: t("11岁  上海"),
          longTime: t("在美术宝学习2年"),
          comment: t("“整体层次分明，色彩丰富细腻，奥、造型生动形象！”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work1.png",
          isSHow: false
        },
        {
          title: t("《我的动物朋友》"),
          name: "John",
          age: t("6岁  美国"),
          longTime: t("在美术宝学习半年"),
          comment: t("“点线面的构成感很棒，形象非常生动可爱。”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work2.png",
          isSHow: false
        },
        {
          title: t("《海边的夜晚》"),
          name: t("周子涵"),
          age: t("8岁  浙江杭州"),
          longTime: t("在美术宝学习了2年"),
          comment: t(" “画面丰富，用色大胆，构图上还有进步空间，加油！~”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work3.png",
          isShow: false
        },
        {
          title: t("《水彩柠檬》"),
          name: t("妮妮"),
          age: t("7岁  北京"),
          longTime: t("在美术宝学习2年"),
          comment: t(
            " “色彩亮丽，水彩水分把控的很好，色彩丰富程度可以再加强哦！”"
          ),

          pic: "https://imgvip.meishubao.com/sea_web/img/work4.png",
          isShow: false
        },
        {
          title: t("《兔子宝宝》"),
          name: "Emily ",
          age: t("10岁  美国"),
          longTime: t("在美术宝学习1年"),
          comment: t("“小兔子的造型非常生动，毛发质感表现的也很好！”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work5.png",
          isSHow: false
        },
        {
          title: t("《森林之王》"),
          name: "Alex ",
          age: t("6岁"),
          longTime: t("在美术宝学习1年"),
          comment: t("宝贝用笔自信大胆，构图饱满，色彩搭配很棒哦！”"),
          pic: "https://imgvip.meishubao.com/sea_web/img/work6.png",
          isSHow: false
        }
      ],

      curOver: false,
      videoWidth: 0,
      videoHeight: 0,
      bannerHeight: 840,
      playing: false,
      selectCode: "",
      selectContact: t("微信"),
      phone: "",
      email: "",
      username: "",
      isName: false,
      isEmail: false,
      isPhone: false,
      nameTips: "",
      emailTips: "",
      phoneTips: "",
      isAgreement: true,
      isEmpty: false,
      showAgreeModal: false,
      showSecretModal: false,
      location: 40,
      showCode: false,
      showContact: false,
      adplatform: "",
      VITE_APP_HK_HOST: import.meta.env.VITE_APP_HK_HOST,
      isHK: false
    };
  },
  watch: {
    showAgreeModal(val) {
      if (val) {
        document.body.setAttribute(
          "style",
          `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`
        );
      } else {
        document.body.removeAttribute("style");
      }
    },
    showSecretModal(val) {
      if (val) {
        document.body.setAttribute(
          "style",
          `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`
        );
      } else {
        document.body.removeAttribute("style");
      }
    },
    playing(val) {
      if (val) {
        document.body.setAttribute(
          "style",
          `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`
        );
      } else {
        document.body.removeAttribute("style");
      }
    },
    adplatform(val) {
      if (val == 12) {
        this.linkinStatics();
      }
      if (val == 5 || val == 6) {
        this.getStaticsFnc()[this.getStaticsType(this.adplatform)].init();
        window.reportAd =
          this.getStaticsFnc()[this.getStaticsType(this.adplatform)].success;
      }
    }
  },
  mounted() {
    let clientWidth = document.documentElement.clientWidth;

    //sessionStorage 防止切换页面，投放首页的参数丢失
    const platform = sessionStorage.getItem("adplatform");
    this.adplatform = this.$route.query.adplatform || platform || "";
    sessionStorage.setItem("adplatform", this.adplatform);
    this.getIp();
    window.addEventListener("scroll", this.handleScroll);
    let channelid = this.$route.query.channel;
    sessionStorage.setItem("channel", channelid);
  },
  methods: {
    async getIp() {
      try {
        let res = await getIplLocation();
        if (res.data && res.data.status === 0) {
          const { data } = res.data;
          const city = data.country;
          codeData.forEach(areaItem => {
            if (areaItem.zh === city) {
              this.selectCode = areaItem.code;
            }
          });
          if (
            res.data.data.region === "香港" &&
            !window.location.host.includes(import.meta.env.VITE_APP_HK_HOST)
          ) {
            console.log("香港");
            this.isHK = true;
          }
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleOver(i) {
      let obj = this.teachArr[i];
      this.$set(obj, "showType", true);
      this.curOver = true;
    },
    handleOut(i) {
      let obj = this.teachArr[i];
      this.$set(obj, "showType", false);
      this.curOver = false;
    },
    handleWorkover(i) {
      let obj = this.workArr[i];
      this.$set(obj, "isShow", true);
    },
    handleWorkout(i) {
      let obj = this.workArr[i];
      this.$set(obj, "isShow", false);
    },
    handlePlayVideo() {
      this.playing = true;
    },
    //动态设置id
    getId(index) {
      return "pos" + (index + 1);
    },
    //教师介绍
    handleTeacher() {
      this.$router.push({
        path: "/teachIntroduction"
      });
    },
    //课程体系
    handleCourse() {
      this.$router.push({
        path: "/setCourse"
      });
    },
    closevideo(e) {
      this.playing = e;
    },
    closeAgreeModal() {
      this.showAgreeModal = false;
      this.showSecretModal = false;
    },
    //选择用户协议
    handleCheck() {
      this.isAgreement = !this.isAgreement;
    },
    // 验证手机号
    handleCheckPhone() {
      if (this.phone === "") {
        this.phoneTips = t("手机号不能为空");
        this.isPhone = true;
      } else if (
        this.phone.length < 4 ||
        !phoneUtil.isValidNumber(
          phoneUtil.parseAndKeepRawInput(`+${this.selectCode}${this.phone}`)
        )
      ) {
        this.phoneTips = t("手机号格式不正确，请重新输入");
        this.isPhone = true;
      }
    },
    // 验证邮箱
    handleCheckEmail() {
      if (this.email === "") {
        this.emailTips = t("联系方式不能为空");
        this.isEmail = true;
      } else if (this.selectContact === t("邮箱")) {
        if (!reg.test(this.email)) {
          this.emailTips = t("邮箱格式有误");
          this.isEmail = true;
          returnFlag = true;
        }
      }
    },
    handleCheckName() {
      if (this.username === "") {
        this.nameTips = t("姓名不能为空");
        this.isName = true;
      }
    },
    handleClearPhone() {
      this.phoneTips = "";
      this.isPhone = false;
    },
    handleClearEmail() {
      this.emailTips = "";
      this.isEmail = false;
    },
    handleClearName() {
      this.nameTips = "";
      this.isName = false;
    },
    //保存注册信息
    handleSave() {
      let returnFlag = false;
      if (this.username === "") {
        this.nameTips = t("姓名不能为空");
        this.isName = true;
        returnFlag = true;
      }
      if (this.selectCode === "") {
        this.phoneTips = t("请选择区号");
        this.isPhone = true;
        returnFlag = true;
      } else if (this.phone === "") {
        this.phoneTips = t("手机号不能为空");
        this.isPhone = true;
        returnFlag = true;
      } else if (
        this.phone.length < 4 ||
        !phoneUtil.isValidNumber(
          phoneUtil.parseAndKeepRawInput(`+${this.selectCode}${this.phone}`)
        )
      ) {
        this.phoneTips = t("手机号格式不正确，请重新输入");
        this.isPhone = true;
        returnFlag = true;
      } else {
        this.isPhone = false;
      }
      if (this.email === "") {
        this.emailTips = t("联系方式不能为空");
        this.isEmail = true;
        returnFlag = true;
      } else if (this.selectContact === t("邮箱")) {
        if (!reg.test(this.email)) {
          this.emailTips = t("邮箱格式有误");
          this.isEmail = true;
          returnFlag = true;
        }
      } else {
        this.isEmail = false;
      }
      if (!this.isAgreement) {
        this.isEmpty = true;
        setTimeout(() => {
          this.isEmpty = false;
        }, 500);
        returnFlag = true;
      }
      if (returnFlag) return false;
      let params = {
        is_join: 1,
        channel:
          this.$route.query.channel ||
          sessionStorage.getItem("channel") ||
          "2144342",
        adplatform: this.$route.query.adplatform || "",
        bd_vid: this.$route.query.bd_vid || "",
        mcode: this.selectCode,
        mobile: this.phone,
        name: this.username,
        orther: `${this.selectContact}：${this.email}`
      };
      this.getLinkin();
      submitPhone(params).then(res => {
        if (res.data.code === 200) {
          if (res.data.is_first) {
            if (this.adplatform == 5 || this.adplatform == 6) {
              this.getStaticsFnc()[
                this.getStaticsType(this.adplatform)
              ].success({
                email: this.selectContact == t("邮箱") ? this.email : "",
                phone_number: this.phone
              });
            }
            this.$router.push({
              path: "/feedBack"
            });
          } else {
            this.$toast(res.data.msg);
          }
        } else {
          this.$toast(res.data.msg);
        }
      });
    },
    changeCode(val) {
      this.selectCode = val;
      this.showCode = false;
    },
    changeContact(val) {
      this.selectContact = val;
      this.showContact = false;
    },
    closePop() {
      this.showCode = false;
    },
    closePopContact() {
      this.showContact = false;
    },
    toggleCode(e) {
      this.closePopContact();
      this.showCode = !this.showCode;
      this.location = e.target.getBoundingClientRect().height + 2;
    },
    toggleContact(e) {
      this.closePop();
      this.showContact = !this.showContact;
      this.location = e.target.getBoundingClientRect().height + 2;
    },
    handleAgreement() {
      this.showAgreeModal = true;
    },
    handleSecret() {
      this.showSecretModal = true;
    },
    hideTip() {
      this.isName = false;
      this.isPhone = false;
      this.isEmail = false;
    },
    linkinStatics() {
      var _linkedin_partner_id = "2462412";
      window._linkedin_data_partner_ids =
        window._linkedin_data_partner_ids || [];
      window._linkedin_data_partner_ids.push(_linkedin_partner_id);
      utils.loadScript(
        `https://snap.licdn.com/li.lms-analytics/insight.min.js`
      );
    },
    getLinkin() {
      const img = document.createElement("img");
      img.src =
        "https://px.ads.linkedin.com/collect/?pid=2462412&conversionId=2917505&fmt=gif";
      img.style = "display:none";
      img.width = 1;
      img.height = 1;
      document.body.appendChild(img);
    },
    getStaticsType(param) {
      let type = "";
      switch (param) {
        case "5":
          type = "google";
          break;
        case "6":
          type = "facebook";
          break;
        default:
          type = "";
          break;
      }
      return type;
    },
    gtag() {
      window.dataLayer.push(arguments);
    },
    googleGTM() {
      utils.loadScript("//cdn.ampproject.org/v0/amp-analytics-0.1.js", {
        "custom-element": "amp-analytics"
      });
      const _dom = document.createElement("amp-analytics");
      _dom.setAttribute(
        "config",
        "https://www.googletagmanager.com/amp.json?id=GTM-T4LZLD3&gtm.url=SOURCE_URL"
      );
      _dom.setAttribute("data-credentials", "include");
      document.body.insertBefore(_dom, document.body.firstChild);
    },
    getStaticsFnc() {
      let obj = {
        google: {
          init: () => {
            this.googleGTM();
            const { gg_id = "", gg_key = "" } = this.$route.query;
            sessionStorage.setItem("adInfo", JSON.stringify({ gg_id, gg_key }));
            console.log("google init", gg_id, gg_key);
            utils.loadScript(
              `//www.googletagmanager.com/gtag/js?id=UA-155116736-1`
            );
            utils.loadScript(`//www.googletagmanager.com/gtag/js?id=${gg_id}`);
            window.dataLayer = window.dataLayer || [];
            this.gtag("js", new Date());
            // ga统计
            this.gtag("config", "UA-155116736-1");
            this.gtag("config", gg_id);
          },
          success: ({ url, email, phone_number }) => {
            const adInfo = JSON.parse(sessionStorage.getItem("adInfo")) || {};
            const { gg_id = "", gg_key = "" } = adInfo;
            const callback = () => {
              if (typeof url != "undefined") {
                window.location = url;
              }
            };

            this.gtag("event", "conversion", {
              send_to: `${gg_id}/${gg_key}`,
              value: 1.0,
              currency: "USD",
              event_callback: callback
            });
            this.gtag("event", "receive_success", {
              event_category: "course"
            });
            this.gtag("event", "leadform", {
              user_data: {
                email,
                phone_number
              }
            });
          }
        },
        facebook: {
          init: () => {
            const { fb_id } = this.$route.query;
            console.log("facebook init", fb_id);
            !(function (f, b, e, v, n, t, s) {
              if (f.fbq) return;
              n = f.fbq = function () {
                n.callMethod
                  ? n.callMethod.apply(n, arguments)
                  : n.queue.push(arguments);
              };
              if (!f._fbq) f._fbq = n;
              n.push = n;
              n.loaded = !0;
              n.version = "2.0";
              n.queue = [];
              t = b.createElement(e);
              t.async = !0;
              t.src = v;
              s = b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t, s);
            })(
              window,
              document,
              "script",
              "https://connect.facebook.net/en_US/fbevents.js"
            );
            window.fbq("init", fb_id);
            window.fbq("track", "PageView");
          },
          success: () => {
            window.fbq("track", "CompleteRegistration");
          }
        }
      };
      return obj;
    }
  },
  components: {
    navHead,
    navFoot,
    videoPlay,
    agreementModal,
    secretModal,
    areaCode,
    contact
  },
  metaInfo: {
    title: t(
      "美术宝Global-在线少儿美术画画平台_在线儿童美术_美术宝1对1海外官网"
    ),
    meta: [
      {
        name: "keywords",
        content: t(
          "美术宝一对一,少儿美术,在线少儿美术,儿童美术,在线儿童美术,美术宝Global，美术宝1对1海外官网"
        )
      },
      {
        name: "description",
        content: t(
          "美术宝Global是拥有数万名美院专业教师的在线儿童美术画画平台！致力于为每个家庭提供个性化定制的美育课程，主要面向4-18岁适龄儿童，采用1对1真人在线互动教学形式，利用iPad、电脑等终端，随时随地的提供少儿艺术培养专属课程。"
        )
      }
    ]
  }
};
</script>
<style lang="less" scoped>
.to-hk {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 999;
  background: #fef8ec;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  span {
    color: #000;
  }
  a {
    color: #ff6430;
    cursor: pointer;
  }
}
#fb-root iframe {
  bottom: 100px !important;
}
.sea-index {
  position: relative;
}
.sea-banner {
  position: relative;
  width: 100%;
  height: 100vh;
  .sea-banner-content {
    position: relative;
    height: 100%;
    overflow: hidden;
    .sea-video-banner {
      height: 100%;
    }
    .mask {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100vh;
      background: rgba(0, 0, 0, 0.2);
    }
  }
  .sea-video-text {
    position: absolute;
    left: 80px;
    top: 276px;
  }
  .sea-banner-info1 {
    width: 279px;
    height: 18px;
  }
  .sea-banner-info2 {
    padding-top: 26px;
    p {
      font-size: 66px;
      font-weight: 600;
      color: #fff;
      line-height: 86px;
    }
  }
  .sea-banner-age {
    font-size: 44px;
    font-weight: 400;
    color: #fff;
    line-height: 44px;
    padding-top: 14px;
  }
  .sea-banner-tags {
    padding-top: 20px;
    .tags {
      display: block;
      float: left;
      color: #fff;
      font-size: 18px;
      background-color: rgba(255, 255, 255, 0.26);
      border-radius: 21px;
      padding: 8px 30px;
      margin-right: 10px;
    }
  }
}
.sea-register {
  position: absolute;
  top: 165px;
  right: 80px;
  width: 340px;
  // height: 552px;
  text-align: center;
  background: rgba(255, 255, 255, 0.88);
  border-radius: 16px;
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 10px;
  z-index: 100;
  .title {
    color: #333;
    font-size: 20px;
    text-align: center;
    padding-top: 32px;
    em {
      color: #ff5938;
    }
  }
  .desc {
    color: #333;
    font-size: 14px;
    text-align: center;
    padding-top: 6px;
    em {
      color: #ff5938;
    }
  }
  .register-form {
    margin-top: 20px;
  }
  .form-item {
    position: relative;
    width: 280px;
    height: 40px;
    line-height: 40px;
    background: #f6f6f6;
    border-radius: 16px;
    margin-bottom: 10px;
    border-radius: 20px;
    border: 1px solid #d8d8d8;
    .tel-code,
    .tel-contact {
      position: absolute;
      left: 0;
      top: 0;
      width: 67px;
      cursor: pointer;
      span {
        font-size: 14px;
        display: inline-block;
        position: relative;
        padding-right: 12px;
        height: 40px;
        &:after {
          content: "";
          position: absolute;
          right: 0;
          top: 17px;
          width: 8px;
          height: 6px;
          background: url("https://imgvip.meishubao.com/sea_web/img/form-more.png")
            no-repeat;
          background-size: 100% 100%;
          transition: all 0.3s;
        }
        &.rotate:after {
          transform: rotate(180deg);
        }
      }
    }
    input {
      width: 100%;
      height: 40px;
      line-height: 40px;
    }
    .tel-input {
      width: 210px;
      margin-left: 78px;
    }
    .email-input {
      width: 210px;
      margin-left: 78px;
    }
    .error-tips {
      top: 0;
      right: 300px;
      &::after {
        position: absolute;
        right: -16px;
        top: 10px;
        display: block;
        content: "";
        width: 0;
        height: 0;
        border-style: solid;
        border-top: 10px transparent dashed;
        border-right: 10px transparent dashed;
        border-bottom: 10px transparent dashed;
        border-left: 10px #ff5938 solid;
        border-radius: 2px;
      }
    }
    .email-tips {
      right: 300px;
    }
  }
  .form-name {
    input {
      width: 240px;
      padding: 0 20px;
    }
  }
  .form-save {
    font-size: 14px;
    color: #fff;
    line-height: 40px;
    font-weight: 400;
    background: #ff5938;
    margin-bottom: 0;
    cursor: pointer;
  }
  .form-agreement {
    text-align: left;
    margin-top: 14px;
    margin-bottom: 16px;
    .check-btn {
      width: 14px;
      height: 14px;
      margin-left: 10px;
      margin-top: 2px;
      img {
        width: inherit;
        height: inherit;
      }
    }
    .agreement-text {
      font-size: 12px;
      margin-left: 8px;
      em {
        color: #3fa1ff;
        cursor: pointer;
      }
    }
  }
  .register-have {
    margin-top: 30px;
    .tit {
      position: relative;
      text-align: center;
      font-size: 16px;
      color: #ff5938;
      img {
        position: absolute;
        top: 10px;
        width: 56px;
        height: 2px;
      }
      .img-l {
        left: 35px;
      }
      .img-r {
        right: 35px;
      }
    }
  }
  .register-have-item {
    padding-top: 20px;
    .have-item-list {
      width: 50%;
      text-align: center;
      margin-bottom: 20px;
      .text {
        display: block;
        color: #666;
        font-size: 14px;
        line-height: 16px;
        margin-top: 10px;
      }
      img {
        width: 62px;
        height: 50px;
        margin: 0 auto;
      }
    }
  }
}
@media screen and (max-width: 1366px) {
  .sea-banner {
    .sea-video-text {
      top: 200px;
      transform: scale(0.8);
    }
  }
  .sea-register {
    top: 106px;
    transform: scale(0.8);
    // top: 146px;
    // width: 270px;
    // height: 423px;
    // padding-left: 21px;
    // padding-right: 21px;
    // .title {
    //     font-size: 14px;
    //     padding-top:23px;
    // }
    // .desc {
    //     font-size: 10px;
    // }
    // .form-item {
    //     width: 199px;
    //     height: 28px;
    //     line-height: 28px;
    //     margin-bottom: 7px;
    //     input {
    //         height: 28px;
    //         line-height: 28px;
    //         font-size: 10px;
    //     }
    // }
    // .form-save {
    //     font-size: 10px;
    // }
    // .form-agreement {
    //     margin-top: 10px;
    //     margin-bottom: 11px;
    //     .check-btn {
    //         width: 10px;
    //         height: 10px;
    //     }
    //     .agreement-text {
    //         font-size: 9px;
    //         margin-left: 6px;
    //     }
    // }
    // .register-form {
    //     margin-top: 14px;
    // }
    // .register-have {
    //     margin-top: 21px;
    //     .tit {
    //         font-size: 11px;
    //         img {
    //             top: 8px;
    //             width: 40px;
    //             height: 1px;
    //         }
    //         .img-l {
    //             left: 25px;
    //         }
    //         .img-r {
    //             right: 25px;
    //         }
    //     }
    // }
    // .register-have-item {
    //     padding-top: 14px;
    //     .have-item-list {
    //         margin-bottom: 14px;
    //         img {
    //             width: 44px;
    //             height: 36px;
    //         }
    //         .text {
    //             margin-top: 7px;
    //         }
    //     }
    // }
  }
}

.sea-video {
  width: 100%;
  text-align: center;
  background: #f7f7f7;
  padding-bottom: 100px;
  .sea-item-title {
    padding-top: 92px;
  }
  .sea-video-content {
    padding-top: 70px;
  }
  .sea-video-l {
    li {
      margin-bottom: 40px;
    }
    .home-icon {
      width: 90px;
      height: 67px;
    }
    .home-desc {
      margin-left: 40px;
    }
    .home-desc-tit {
      font-size: 24px;
      color: #333;
      line-height: 33px;
    }
    .home-desc-info {
      text-align: left;
      margin-top: 10px;
      p {
        font-size: 16px;
        color: #666;
        line-height: 24px;
      }
    }
  }
  .sea-video-r {
    position: relative;
    width: 566px;
    height: 326px;
    margin-left: 128px;
    margin-top: 82px;
    .poster {
      width: inherit;
      height: inherit;
    }
    .play-icon {
      position: absolute;
      left: 50%;
      top: 50%;
      width: 120px;
      height: 120px;
      margin-left: -60px;
      margin-top: -60px;
      cursor: pointer;
    }
  }
}
.sea-teacher {
  position: relative;
  text-align: center;
  width: 100%;
  height: 958px;
  .hover {
    .small-text {
      display: inline-block;
      font-size: 18px;
      line-height: 21px;
      vertical-align: middle;
      padding: 0 21px;
    }
  }
  .sea-teacher-content {
    position: relative;
    margin-top: 50px;
    .advantage-item {
      position: absolute;
      width: 180px;
      height: 180px;
      line-height: 180px;
      text-align: center;
      font-size: 32px;
      color: #fff;
      background: #ff5938;
      border-radius: 90px;
      margin-right: 70px;
      cursor: pointer;
      z-index: 9;
    }
    #pos1 {
      left: 136px;
      top: 90px;
    }
    #pos2 {
      left: 386px;
      top: 0;
    }
    #pos3 {
      left: 636px;
      top: 0;
    }
    #pos4 {
      left: 886px;
      top: 90px;
    }
    .sea-teacher-circle {
      position: absolute;
      width: 50px;
      height: 50px;
      background: rgba(255, 88, 55, 0.1);
      border-radius: 50%;
    }
    .circle1 {
      left: 45px;
      top: 70px;
    }
    .circle2 {
      left: 983px;
      top: 12px;
      width: 46px;
      height: 46px;
    }
    .circle3 {
      left: 2px;
      top: 350px;
      width: 100px;
      height: 100px;
    }
    .circle4 {
      right: 0;
      top: 266px;
      width: 102px;
      height: 102px;
    }
  }
  .sea-teacher-img {
    position: absolute;
    left: 50%;
    bottom: 26px;
    margin-left: -443px;
    width: 886px;
    height: 531px;
    z-index: 3;
  }
  .bottom-bg {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 232px;
    z-index: 4;
  }
  .sea-teacher-more {
    position: absolute;
    left: 50%;
    bottom: 66px;
    margin-left: -105px;
    width: 210px;
    height: 50px;
    line-height: 50px;
    background: #fff;
    border-radius: 25px;
    z-index: 5;
    cursor: pointer;
    img {
      width: 42px;
      height: 42px;
      margin-left: 4px;
      margin-top: 4px;
    }
    span {
      color: #333;
      font-size: 16px;
      margin-left: 27px;
    }
  }
  .sea-dev-button {
    position: absolute;
    left: 50%;
    bottom: 30px;
    margin-left: -140px;
    z-index: 6;
  }
}
.sea-ready {
  text-align: center;
  width: 100%;
  background: #f7f7f7;
  padding-bottom: 100px;
  .sea-ready-content {
    margin-top: 80px;
    .sea-ready-item {
      display: inline-block;
      zoom: 1;
      margin-right: 180px;
      img {
        width: 230px;
        height: 230px;
      }
      .ready-item-title {
        width: 230px;
        font-size: 28px;
        color: #ff5938;
        line-height: 30px;
        padding-top: 40px;
      }
      .ready-item-desc {
        margin-top: 20px;
        p {
          font-size: 20px;
          color: #666;
          line-height: 33px;
        }
      }
    }
    .sea-ready-last {
      margin-right: 0;
    }
  }
}
.sea-development {
  text-align: center;
  padding-bottom: 100px;
  .sea-development-content {
    margin-top: 60px;
    img {
      width: 1200px;
      height: 394px;
    }
  }
  .sea-dev-button {
    margin-top: 80px;
  }
}
.sea-service {
  text-align: center;
  background: #f7f7f7;
  padding-bottom: 106px;
  .sea-service-content {
    text-align: center;
    margin-top: 59px;
  }
  .service-item-c {
    display: inline-block;
    vertical-align: middle;
    margin-left: 60px;
    margin-right: 60px;
    img {
      width: 528px;
      height: 528px;
    }
  }
  .service-item-l {
    display: inline-block;
    vertical-align: middle;
    text-align: right;
    .item-tags {
      height: 52px;
    }
    .tags {
      width: 144px;
      height: 52px;
      line-height: 52px;
      text-align: center;
      font-size: 26px;
      color: #fff;
      background: #ff843f;
      border-radius: 21px 21px 21px 0px;
    }
    .child {
      float: right;
      overflow: hidden;
      background: #ffde52;
      border-radius: 21px 21px 0px 21px;
    }
    .item-list {
      margin-top: 54px;
      .item-list-title {
        font-size: 26px;
        font-weight: 400;
        color: #ff5938;
        line-height: 28px;
      }
      .item-list-desc {
        margin-top: 7px;
        p {
          font-size: 18px;
          color: #333;
          font-weight: 400;
          line-height: 26px;
        }
      }
    }
  }
  .service-item-r {
    text-align: left;
  }
}
.sea-work {
  text-align: center;
  padding-bottom: 50px;
  .sea-work-content {
    margin-top: 80px;
    .sea-work-item {
      position: relative;
      width: 350px;
      height: 350px;
      margin-right: 75px;
      margin-bottom: 70px;
      cursor: pointer;
      img {
        width: 100%;
        height: auto;
      }
    }
    .work-text {
      background: #ff5938;
      img {
        width: 250px;
        height: 2px;
      }
    }
    .work-last {
      margin-right: 0;
    }
    .work-item-student {
      position: absolute;
      left: 0;
      top: 0;
      width: 350px;
      height: 230px;
      background: #ff5938;
      opacity: 0.9;
      padding-top: 63px;
      padding-bottom: 57px;
      .work-info {
        font-size: 26px;
        color: #fff;
        line-height: 28px;
        margin-bottom: 10px;
      }
      .work-info1 {
        font-size: 16px;
        color: #fff;
        line-height: 22px;
        margin-bottom: 10px;
      }
      .work-line {
        width: 250px;
        height: 2px;
        margin: 0 50px 10px;
      }
      .work-info2 {
        font-size: 16px;
        color: #fff;
        line-height: 26px;
        padding: 0 52px;
      }
    }
  }
}
.sea-medio {
  text-align: center;
  width: 100%;
  background: #f7f7f7;
  padding-bottom: 120px;
  .sea-medio-content {
    margin-top: 60px;
    img {
      width: 1200px;
      height: 384px;
    }
  }
}
.sea-investment {
  text-align: center;
  padding-bottom: 91px;
  .sea-investment-content {
    margin-top: 34px;
    img {
      width: 1200px;
      height: 439px;
    }
  }
}
.sea-promise {
  text-align: center;
  width: 100%;
  height: 569px;
  background: url(https://imgvip.meishubao.com/sea_web/img/promise-bg.png)
    no-repeat;
  background-size: cover;
  .sea-item-title,
  .sea-promise,
  .sea-item-desc {
    color: #fff;
  }
  .sea-promise-content {
    margin-top: 80px;
    .child-last {
      margin-right: 0;
    }
  }
  .promise-item {
    width: 370px;
    height: 160px;
    background: #fff;
    border-radius: 30px;
    border: 6px solid #ff775c;
    margin-right: 20px;
    .promise-item-img {
      width: 61px;
      height: 44px;
      margin-left: 36px;
      margin-top: 58px;
      margin-right: 32px;
    }
    .promise-item-img1 {
      width: 46px;
      height: 48px;
      margin-left: 45px;
      margin-top: 56px;
      margin-right: 38px;
    }
    .promise-item-img2 {
      width: 56px;
      height: 53px;
      margin-left: 38px;
      margin-top: 54px;
      margin-right: 35px;
    }
    .promise-item-text {
      text-align: left;
      margin-top: 25px;
      margin-bottom: 28px;
      .title {
        font-size: 26px;
        color: #ff5837;
        line-height: 37px;
      }
      .text {
        margin-top: 6px;
      }
      p {
        font-size: 20px;
        color: #adadad;
        line-height: 32px;
      }
    }
  }
}

.sea-button {
  width: 280px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  color: #fff;
  background: #ff5938;
  border-radius: 20px;
  margin: 20px auto 0;
  cursor: pointer;
}
</style>
