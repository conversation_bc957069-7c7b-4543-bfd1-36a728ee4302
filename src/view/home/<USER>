<template>
  <div>
    <Hk v-if="isHK"></Hk>
    <Cn v-else></Cn>
  </div>
</template>

<script>
import hk from "./components/hk.vue";
import cn from "./components/cn.vue";
import i18n from "@/locales";

export default {
  name: "index",
  components: {
    Hk: hk,
    Cn: cn
  },
  data() {
    return {
      isHK: false
    };
  },
  mounted() {
    if (window.location.host.includes(import.meta.env.VITE_APP_HK_HOST)) {
      this.isHK = true;
      i18n.locale = 'zh-HK'
    }
  },
  methods: {}
};
</script>
<style lang="less" scoped></style>
