<template>
  <div class="feedback-box">
    <nav-head :showBg="showBg"></nav-head>
    <div class="sea-main feedback-info">
      <img
        src="https://global-static.meishubao.com/sea_web/img/serviced-btn.png"
        class="serviced-btn"
      />
      <div class="feedback-content clearfix">
        <div class="feedback-content-l fl">
          <img
            src="https://global-static.meishubao.com/sea_web/img/feedback-img.png"
          />
        </div>
        <div class="feedback-content-r fl">
          <div class="content-r-item">
            <h3 class="title">{{ $t("上课流程") }}</h3>
            <div class="process clearfix">
              <img
                src="https://global-static.meishubao.com/sea_web/img/process-bg1.png"
                class="fl"
              />
              <img
                src="https://global-static.meishubao.com/sea_web/img/process-bg2.png"
                class="fl"
              />
              <img
                src="https://global-static.meishubao.com/sea_web/img/process-bg3.png"
                class="fl"
              />
              <img
                src="https://global-static.meishubao.com/sea_web/img/process-bg4.png"
                class="fl last-img"
              />
            </div>
          </div>
          <!-- <div class="content-r-item two-item">
                        <h3 class="title">上课流程</h3>
                        <div class="process">
                            <p class="tips">我们已经发送邮件给您，如果您在5-10分钟内没有收到邮件，可以查看垃圾邮箱；课程顾问老师将会在24小时内联系您，为您安排试听课，请您保持电话通畅，耐心等待！</p>
                        </div>
                    </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import navHead from "@/components/head";
export default {
  name: "downloadCenter",
  data() {
    return {
      showBg: false
    };
  },
  components: {
    navHead
  }
};
</script>
<style lang="less" scoped>
.feedback-info {
  text-align: center;
  margin-top: 190px;
  .serviced-btn {
    width: 361px;
    height: 72px;
  }
  .feedback-content {
    margin-top: 60px;
    .feedback-content-l {
      width: 432px;
      height: 360px;
      margin-top: 13px;
      img {
        width: inherit;
        height: inherit;
      }
    }
  }
  .feedback-content-r {
    text-align: left;
    margin-left: 132px;
    .two-item {
      margin-top: 40px;
    }
    .title {
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333;
      line-height: 24px;
    }
    .process {
      margin-top: 30px;
      img {
        display: block;
        width: 144px;
        height: 186px;
        margin-right: 20px;
      }
      .last-img {
        margin-right: 0;
      }
      .tips {
        width: 547px;
        height: 40px;
        background: #f6f6f6;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666;
        line-height: 20px;
        padding: 25px 45px 25px 44px;
        border-radius: 10px;
      }
    }
  }
}
</style>
