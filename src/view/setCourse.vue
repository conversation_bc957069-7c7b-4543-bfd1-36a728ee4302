<template>
  <div class="sea-course">
    <nav-head></nav-head>
    <div class="sea-course-banner">
      <div class="sea-main">
        <h3 class="title">{{ $t("个性化美育定制专家") }}</h3>
        <p class="desc">{{ $t("与国际接轨的儿童美术教育课程") }}</p>
      </div>
    </div>
    <div class="sea-course-value">
      <div class="sea-main sea-value-content">
        <h3 class="sea-item-title">{{ $t("在课程中，孩子将会收获什么？") }}</h3>
        <p class="sea-item-desc">
          {{ $t("培养孩子智能、心理、思维美育等各项潜能发展") }}
        </p>
        <img
          src="https://global-static.meishubao.com/sea_web/img/course-main-value.png"
          class="value-img"
        />
      </div>
    </div>
    <div class="sea-course-step">
      <div class="sea-main">
        <h3 class="sea-item-title">{{ $t("主修课程体系进阶图") }}</h3>
        <p class="sea-item-desc">{{ $t("自主研发 | 专属定制 | 接轨国际") }}</p>
        <div class="sea-step-tab clearfix">
          <template v-for="(item, i) in tabs">
            <span
              class="step-tab-item fl"
              :class="{ active: i === curIndex, 'last-item': i === 6 }"
              :key="i"
              @click="handleTabs(i)"
              >{{ item }}</span
            >
          </template>
        </div>
        <div class="sea-step-content">
          <template v-for="(item, i) in imgArr">
            <img :src="item" :key="i" v-show="i == curIndex" />
          </template>
        </div>
        <div class="sea-course-info">
          <p>
            {{
              $t(
                "美术宝1v1课程体系3.0版，基于我国基础教育新课程标准、美术学科核心素养，结合美国STEAM艺术教育课程体、我国优秀经典传统文化、跨学科知识、专注力培养等多方面，潜心研发的符合儿童身心发展及认知规律、开放多元的国际少儿美术课程体系，课程以美术宝独有的ARTCLASS在线直播技术为授课平台，为全球4-18岁儿童提供便捷和高品质美术教学服务。"
              )
            }}
          </p>
          <p>
            {{
              $t(
                "课程共包含六大经典科目：创意绘画（含线描）、油画、国画、动漫、彩铅、素描（含速写），每个经典画科从易到难包含6个级别，每个级别为48节全年课包，每个课包含6个单元，每单元设置为8节课。整套体系满足4-18岁适龄儿童不同难度，不同画科需求。"
              )
            }}
          </p>
        </div>
      </div>
    </div>
    <div class="sea-course-school">
      <div class="sea-main">
        <h3 class="sea-item-title">{{ $t("丰富实用的学习内容") }}</h3>
        <p class="sea-item-desc">
          {{ $t("让孩子在绘画世界中尽情创造、开阔眼界、增长知识") }}
        </p>
      </div>
      <div class="sea-course-content">
        <div class="sea-main">
          <img
            src="https://global-static.meishubao.com/sea_web/img/course-main-school.png"
            class="value-img"
          />
        </div>
      </div>
    </div>
    <div class="sea-course-model">
      <div class="sea-main">
        <h3 class="sea-item-title">{{ $t("右脑开发五维模型") }}</h3>
        <p class="sea-item-desc">{{ $t("孩子收获的绝不仅仅是几张作品") }}</p>
        <img
          src="https://global-static.meishubao.com/sea_web/img/course-main-model.png"
          class="value-img"
        />
      </div>
    </div>
    <div class="sea-course-target">
      <div class="sea-main">
        <h3 class="sea-item-title">{{ $t("五维一体教学目标核心理论") }}</h3>
        <p class="sea-item-desc">{{ $t("让孩子每一次画画都有价值") }}</p>
        <div class="course-target-box clearfix">
          <div class="course-target-item fl">
            <img
              src="https://global-static.meishubao.com/sea_web/img/target-icon1.png"
              class="target-img"
            />
            <p class="title">{{ $t("知识目标") }}</p>
            <p class="desc">
              {{ $t("通过绘画了解与现实生活紧密结合的尝试知识") }}
            </p>
          </div>
          <div class="course-target-item fl">
            <img
              src="https://global-static.meishubao.com/sea_web/img/target-icon2.png"
              class="target-img"
            />
            <p class="title">{{ $t("技能目标") }}</p>
            <p class="desc">
              {{ $t("运用轻松有趣的教学方法引导学生掌握基本绘画技能") }}
            </p>
          </div>
          <div class="course-target-item fl">
            <img
              src="https://global-static.meishubao.com/sea_web/img/target-icon3.png"
              class="target-img"
            />
            <p class="title">{{ $t("情感目标") }}</p>
            <p class="desc">
              {{ $t("通过绘画让孩子培养学习兴趣，养成优秀个人品质") }}
            </p>
          </div>
          <div class="course-target-item fl">
            <img
              src="https://global-static.meishubao.com/sea_web/img/target-icon4.png"
              class="target-img"
            />
            <p class="title">{{ $t("行为目标") }}</p>
            <p class="desc">
              {{ $t("通过课堂引导孩子正确行为习惯、培养表达能力") }}
            </p>
          </div>
          <div class="course-target-item last-child fl">
            <img
              src="https://global-static.meishubao.com/sea_web/img/target-icon5.png"
              class="target-img"
            />
            <p class="title">{{ $t("应用目标") }}</p>
            <p class="desc">
              {{ $t("孩子能够学以致用，将课堂所学知识转化为个人知识") }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <nav-foot></nav-foot>
  </div>
</template>
<script>
import navHead from "@/components/head";
import navFoot from "@/components/foot";
import { t } from "i18n";
export default {
  name: "setCourse",
  data() {
    return {
      tabs: [
        t("体系"),
        t("创意画"),
        t("国画"),
        t("色彩"),
        t("彩铅"),
        t("动漫"),
        t("素描")
      ],
      imgArr: [
        "https://global-static.meishubao.com/sea_web/img/course-main-step7_b.png",
        "https://global-static.meishubao.com/sea_web/img/course-main-step6_b.png",
        "https://global-static.meishubao.com/sea_web/img/course-main-step4_b.png",
        "https://global-static.meishubao.com/sea_web/img/course-main-step3_b.png",
        "https://global-static.meishubao.com/sea_web/img/course-main-step1_b.png",
        "https://global-static.meishubao.com/sea_web/img/course-main-step5_b.png",
        "https://global-static.meishubao.com/sea_web/img/course-main-step2_b.png"
      ],

      curIndex: 0
    };
  },
  methods: {
    handleTabs(i) {
      this.curIndex = i;
    }
  },
  components: {
    navHead,
    navFoot
  },
  metaInfo: {
    title: t("美术宝Global-课程体系_个性定制化课程_美术宝1对1海外官网"),
    meta: [
      {
        name: "keywords",
        content: t(
          "美术宝一对一,少儿美术,在线少儿美术,儿童美术,在线儿童美术,美术宝Global课程体系，美术宝1对1海外官网"
        )
      },
      {
        name: "description",
        content: t(
          "美术宝Global课程体系基于中国基础教育新课程标准、美术学科核心素养，结合美国STEAM艺术教育课程体系、中国优秀经典传统文化、跨学科知识、专注力培养等多方面，潜心研发的符合儿童身心发展及认知规律、开放多元的国际少儿美术课程体系，生动活泼、持续优化的课程内容，让孩子爱上画画，快乐画画。"
        )
      }
    ]
  }
};
</script>
<style lang="less" scoped>
.sea-course-banner {
  width: 100%;
  height: 687px;
  background: url(https://global-static.meishubao.com/sea_web/img/course-banner.png)
    no-repeat;
  background-size: cover;
  .title {
    font-size: 52px;
    font-weight: 600;
    color: #fff;
    line-height: 72px;
    padding-top: 257px;
  }
  .desc {
    font-size: 38px;
    font-weight: 400;
    color: #fff;
    line-height: 48px;
    padding-top: 15px;
  }
}
.sea-course-value {
  text-align: center;
  .sea-value-content {
    position: relative;
    .value-img {
      width: 1200px;
      height: 797px;
      margin-top: 78px;
      vertical-align: bottom;
    }
  }
}
.sea-course-step {
  text-align: center;
  width: 100%;
  background: #f7f7f7;
  padding-bottom: 100px;
  .sea-step-tab {
    text-align: center;
    margin-top: 60px;
    margin-left: 95px;
    .step-tab-item {
      width: 130px;
      height: 52px;
      line-height: 52px;
      font-size: 22px;
      color: #ff6b52;
      border: 1px solid #ff6b52;
      font-weight: 400;
      border-radius: 26px;
      margin-right: 20px;
      cursor: pointer;
    }
    .last-item {
      margin-right: 0 !important;
    }
    .active {
      color: #fff !important;
      background: #ff6b52;
    }
  }
  .sea-step-content {
    margin-top: 50px;
    img {
      width: 1200px;
      height: 664px;
    }
  }
  .sea-course-info {
    font-size: 18px;
    font-weight: 400;
    color: #333;
    text-align: left;
    line-height: 36px;
    margin-top: 34px;
  }
}
.sea-course-school {
  text-align: center;
  width: 100%;
  padding-bottom: 72px;
  .value-img {
    width: 994px;
    height: 635px;
    margin-top: 37px;
  }
  .sea-course-content {
    background: #fff
      url(https://global-static.meishubao.com/sea_web/img/course-school-bg.png)
      no-repeat;
    background-position: 50% 50%;
    margin-top: 38px;
    padding-top: 85px;
    padding-bottom: 27px;
  }
}
.sea-course-model {
  width: 100%;
  height: 900px;
  text-align: center;
  background: url(https://global-static.meishubao.com/sea_web/img/course-model-bg.png)
    no-repeat;
  background-size: cover;
  .value-img {
    width: 1200px;
    height: 598px;
    margin-top: 60px;
    margin-bottom: 46px;
  }
}
.sea-course-target {
  text-align: center;
  padding-bottom: 100px;
  .course-target-box {
    margin-top: 59px;
    margin-left: 3px;
    .last-child {
      margin-right: 0 !important;
    }
    .course-target-item {
      width: 204px;
      margin-right: 43px;
      overflow: hidden;
      img {
        width: 144px;
        height: 144px;
      }
      .title {
        color: #333;
        font-size: 30px;
        line-height: 32px;
      }
      .desc {
        color: #999;
        font-size: 16px;
        line-height: 26px;
        margin-top: 20px;
      }
    }
  }
}
</style>
