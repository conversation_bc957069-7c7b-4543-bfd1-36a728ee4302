<template>
  <div class="about">
    <nav-head :logo-type="1"></nav-head>
    <div class="banner-wrap">
      <div class="banner sea-main">
        <div class="banner-left">
          <p>{{ $t("我们的使命") }}</p>
          <p>{{ $t("用科技推动艺术进步") }}</p>
          <div class="bottom">
            {{ $t("学员遍布") }}<span>94</span>{{ $t("个国家和地区") }}
          </div>
        </div>
        <div class="banner-right">
          <img
            src="https://global-static.meishubao.com/sea_web/img/about_img1.png"
            :alt="$t('美术宝')"
          />
        </div>
      </div>
    </div>
    <div class="about-part1">
      <h4>{{ $t("认识我们") }}</h4>
      <div class="part1-wrap sea-main">
        <div class="part1-left">
          <img
            src="https://global-static.meishubao.com/sea_web/img/about_img2.png"
            :alt="$t('美术宝')"
          />
        </div>
        <div class="part1-right">
          {{
            $t(
              "美术宝1对1致力于为每个家庭提供个性化定制的美育课程，主要面向4-18岁适龄儿童，采用1对1真人在线互动教学形式，利用iPad或电脑等移动终端，随时随地的提供少儿艺术培养专属课程，自主研发的美术教学“ArtClass”跨平台在线互动课堂系统，实现在线美术教学的多场景应用，让用户足不出户就能随时接受来自央美、清华、国美等数万名师的专业辅导。"
            )
          }}
        </div>
      </div>
    </div>
    <div class="about-video">
      <div class="poster">
        <p class="top">{{ $t("4分钟了解美术宝1对1") }}</p>
        <img
          @click="playVideo"
          src="https://global-static.meishubao.com/sea_web/img/video_play.png"
          :alt="$t('美术宝')"
        />
        <p class="bottom">{{ $t("— 点击播放美术宝1对1介绍视频 —") }}</p>
      </div>
    </div>
    <div class="about-part2">
      <h4>{{ $t("发展历程") }}</h4>
      <div class="sea-main">
        <img
          src="https://global-static.meishubao.com/sea_web/img/about_img3.png?v=202008"
          :alt="$t('美术宝')"
        />
      </div>
    </div>
    <div class="about-part3">
      <h4>{{ $t("媒体报道") }}</h4>
      <div class="sea-main">
        <img
          src="https://global-static.meishubao.com/sea_web/img/about_img4.png"
          :alt="$t('美术宝')"
        />
      </div>
    </div>
    <div class="about-part4">
      <h4>{{ $t("企业荣誉") }}</h4>
      <p>{{ $t("认可我们的绝不仅仅是用户") }}</p>
      <div class="sea-main">
        <img
          src="https://global-static.meishubao.com/sea_web/img/about_img5.png"
          :alt="$t('美术宝')"
        />
      </div>
    </div>
    <div class="about-part5 sea-main">
      <h4>{{ $t("联系我们") }}</h4>
      <div class="contact-form sea-main">
        <div class="clearfix">
          <div class="input-wrap input-name fl">
            <p class="input-label">
              {{ $t("您的姓名")
              }}<span v-if="userTip.name" class="tip">{{ userTip.name }}</span>
            </p>
            <input
              v-model="userInfo.name"
              type="text"
              :placeholder="$t('请输入姓名')"
            />
          </div>
          <div class="input-wrap input-email fr">
            <p class="input-label">
              {{ $t("您的邮箱")
              }}<span v-if="userTip.email" class="tip">{{
                userTip.email
              }}</span>
            </p>
            <input
              v-model="userInfo.email"
              type="text"
              :placeholder="$t('请输入邮箱')"
            />
          </div>
        </div>
        <div class="input-wrap">
          <p class="input-label">
            {{ $t("您的电话")
            }}<span v-if="userTip.phone" class="tip">{{ userTip.phone }}</span>
          </p>
          <div class="phone-wrap">
            <area-code
              :isShow="showCode"
              :location="location"
              @changeCode="changeCode"
              @closePop="closePop"
            ></area-code>
            <div class="code" @click.stop="toggleCode">
              <span :class="{ rotate: showCode }">{{
                selectCode ? `+${selectCode}` : $t("请选择")
              }}</span>
            </div>
            <input
              v-model="userInfo.phone"
              type="text"
              :placeholder="$t('请输入电话')"
            />
          </div>
        </div>
        <div class="input-wrap">
          <p class="input-label">
            {{ $t("留言")
            }}<span v-if="userTip.feedback" class="tip">{{
              userTip.feedback
            }}</span>
          </p>
          <textarea
            class="input-textarea"
            v-model="userInfo.feedback"
            :placeholder="$t('请输入留言')"
          ></textarea>
        </div>
        <div class="btn" @click="handleSubmit">{{ $t("提交") }}</div>
      </div>
    </div>
    <loading :showLoading="showLoading"></loading>
    <video-play
      v-if="isPlay"
      @closed="closeVideo"
      videoSrc="https://global-static.meishubao.com/sea_web/video/about.mp4"
    ></video-play>
    <nav-foot></nav-foot>
  </div>
</template>
<script>
import navHead from "@/components/head";
import navFoot from "@/components/foot";
import areaCode from "@/components/areaCode";
import videoPlay from "@/components/videoPlay";
import loading from "@/components/loading";
import { submitFeedback } from "@/api/api.js";
import { t } from "i18n";
export default {
  metaInfo: {
    title: t("美术宝Global-关于我们_美术宝1对1海外官网"),
    meta: [
      {
        name: "keywords",
        content: t(
          "美术宝一对一,少儿美术,在线少儿美术,儿童美术,在线儿童美术,关于美术宝Global，美术宝1对1海外官网"
        )
      },
      {
        name: "description",
        content: t(
          "美术宝Global是美术宝潜心研发多年推出的美术教学平台，自主研发的互动一对一真人教学模式，依靠电脑iPad等移动终端，采用真人Art Loop实时视频透视矫正系统，拥有专利摄像头和AI透视算法，还原线下授课场景，让更多的孩子足不出户，随时随地接受来自央美、清华、国美、圣马丁等国内外知名艺术院校的美术状元及名师教授的专业互动指导。"
        )
      }
    ]
  },
  data() {
    return {
      userInfo: {
        name: "",
        email: "",
        phone: "",
        feedback: ""
      },
      userTip: {
        name: "",
        email: "",
        phone: "",
        feedback: ""
      },
      isPlay: false,
      selectCode: "",
      showCode: false,
      location: 60,
      showLoading: false
    };
  },
  components: {
    navHead,
    navFoot,
    areaCode,
    videoPlay,
    loading
  },
  watch: {
    userInfo: {
      handler: function (val) {
        if (val.name) {
          this.userTip.name = "";
        }
        if (val.email) {
          this.userTip.email = "";
        }
        if (val.phone) {
          this.userTip.phone = "";
        }
        if (val.feedback) {
          this.userTip.feedback = "";
        }
      },
      deep: true
    },
    isPlay(val) {
      if (val) {
        document.body.setAttribute(
          "style",
          `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`
        );
      } else {
        document.body.removeAttribute("style");
      }
    }
  },
  methods: {
    handleSubmit() {
      const emailReg = new RegExp(
        "^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$"
      );
      let returnFlag = false;
      if (!this.userInfo.name) {
        this.userTip.name = t("不能为空");
        returnFlag = true;
      } else {
        this.userTip.name = "";
      }
      if (!this.userInfo.email) {
        this.userTip.email = t("不能为空");
        returnFlag = true;
      } else if (!emailReg.test(this.userInfo.email)) {
        this.userTip.email = t("格式错误");
        returnFlag = true;
      } else {
        this.userTip.email = "";
      }
      if (this.selectCode === "") {
        this.userTip.phone = t("请选择区号");
        returnFlag = true;
      } else if (!this.userInfo.phone) {
        this.userTip.phone = t("不能为空");
        returnFlag = true;
      } else {
        this.userTip.phone = "";
      }
      if (!this.userInfo.feedback) {
        this.userTip.feedback = t("不能为空");
        returnFlag = true;
      } else {
        this.userTip.feedback = "";
      }
      if (returnFlag) return false;
      let data = {
        name: this.userInfo.name,
        email: this.userInfo.email,
        code: this.selectCode,
        phone: this.userInfo.phone,
        message: this.userInfo.feedback
      };
      this.showLoading = true;
      submitFeedback(data).then(res => {
        this.showLoading = false;
        if (res.data.code == 200) {
          this.$toast(t("反馈成功"));
          window.reportAd &&
            window.reportAd({
              email: this.userInfo.email,
              phone_number: this.userInfo.phone
            });
        } else {
          this.$toast(res.data.message);
        }
      });
    },
    playVideo() {
      this.isPlay = true;
    },
    closeVideo() {
      this.isPlay = false;
    },
    toggleCode(e) {
      this.showCode = !this.showCode;
      this.location = e.target.getBoundingClientRect().height + 2;
    },
    changeCode(val) {
      this.selectCode = val;
      this.showCode = false;
    },
    closePop() {
      this.showCode = false;
    }
  }
};
</script>
<style lang="less" scoped>
.banner-wrap {
  position: relative;
  width: 100%;
  height: 687px;
  background: #ff5837;
  .banner {
    position: absolute;
    top: 177px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
  }
  .banner-left {
    flex: 1;
    padding-top: 80px;
    p {
      font-size: 52px;
      font-weight: 600;
      color: #fff;
      line-height: 72px;
    }
    .bottom {
      padding-top: 20px;
      font-size: 38px;
      font-family: "PingFang SC";
      color: #fff;
      span {
        font-size: 48px;
      }
    }
  }
  .banner-right {
    width: 687px;
    height: 422px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.about {
  h4 {
    font-size: 46px;
    line-height: 48px;
    color: #333;
    padding: 100px 0 60px;
    text-align: center;
  }
  img {
    vertical-align: middle;
  }
}
.about-part1 {
  .part1-wrap {
    padding: 20px 0 100px;
    display: flex;
    align-items: center;
    .part1-left {
      width: 582px;
      height: 388px;
      margin-right: 82px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .part1-right {
      width: 536px;
      line-height: 44px;
      font-size: 26px;
      color: #777;
      text-align: justify;
    }
  }
}
.about-part2 {
  padding-bottom: 100px;
  background: #f7f7f7;
  img {
    width: 100%;
  }
}
.about-part3 {
  padding-bottom: 120px;
  img {
    width: 100%;
  }
}
.about-part4 {
  background: #f7f7f7;
  padding-bottom: 128px;
  h4 {
    padding-bottom: 20px;
  }
  p {
    font-size: 26px;
    line-height: 28px;
    color: #999;
    padding-bottom: 80px;
    text-align: center;
  }
  img {
    width: 100%;
  }
}
.about-part5 {
  padding-bottom: 100px;
  .input-wrap {
    position: relative;
    padding-bottom: 20px;
    .tip {
      position: absolute;
      top: 0;
      right: 10px;
      color: #f00;
    }
    .input-label {
      font-size: 20px;
      padding-bottom: 14px;
    }
    input {
      width: 100%;
      height: 58px;
      line-height: 58px;
      padding-left: 23px;
      font-size: 22px;
      border-radius: 8px;
      border: 1px solid rgba(219, 219, 219, 1);
      box-sizing: border-box;
    }
    .input-textarea {
      width: 100%;
      height: 280px;
      padding-left: 23px;
      padding-top: 16px;
      font-size: 22px;
      border-radius: 8px;
      border: 1px solid rgba(219, 219, 219, 1);
      box-sizing: border-box;
      resize: none;
      outline: none;
    }
  }
  .phone-wrap {
    position: relative;
    .code {
      position: absolute;
      top: 0;
      left: 0;
      width: 100px;
      line-height: 58px;
      font-size: 22px;
      text-align: center;
      cursor: pointer;
      &:after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 22px;
        background-color: #ededed;
      }
      span {
        display: inline-block;
        position: relative;
        padding-right: 18px;
        height: 58px;
        &:after {
          content: "";
          position: absolute;
          right: 0;
          top: 25px;
          width: 12px;
          height: 8px;
          background: url("https://global-static.meishubao.com/sea_web/img/form-more.png")
            no-repeat;
          background-size: 100% 100%;
          transition: all 0.3s;
        }
        &.rotate:after {
          transform: rotate(180deg);
        }
      }
    }
    input {
      padding-left: 115px;
    }
  }
  .input-name {
    width: 530px;
  }
  .input-email {
    width: 640px;
  }
  .btn {
    width: 280px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #fff;
    background: rgba(255, 89, 56, 1);
    border-radius: 20px;
    margin: 30px auto 0;
    cursor: pointer;
  }
}
.about-video {
  // position: relative;
  video {
    width: 100%;
    height: 650px;
  }
  .poster {
    // position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 650px;
    text-align: center;
    background: url("https://global-static.meishubao.com/sea_web/img/about_video.png")
      no-repeat center center;
    background-size: cover;
    z-index: 1;
    .top {
      font-size: 46px;
      color: #fff;
      padding-top: 100px;
    }
    .bottom {
      font-size: 26px;
      color: #fff;
    }
    img {
      width: 148px;
      height: 148px;
      margin: 110px 0 115px;
      cursor: pointer;
    }
  }
}
</style>
