import { t } from "i18n";
const routes = [
  {
    path: "/",
    meta: {
      title: t("首页")
    },
    component: () => import("@/view/home/<USER>")
  },
  {
    path: "/teachIntroduction",
    name: "teachIntroduction",
    meta: {
      title: t("师资介绍")
    },
    component: () => import("@/view/teachIntroduction")
  },
  {
    path: "/setCourse",
    name: "setCourse",
    meta: {
      title: t("课程设置")
    },
    component: () => import("@/view/setCourse")
  },
  // {
  //   path: '/downloadCenter',
  //   name: 'downloadCenter',
  //   meta: {
  //     title:  '下载中心'
  //   },
  //   component: () => import ('@/view/downloadCenter'),
  // },
  {
    path: "/about",
    name: "about",
    meta: {
      title: t("关于我们")
    },
    component: () => import("@/view/about")
  },
  {
    path: "/feedBack",
    name: "feedBack",
    meta: {
      title: t("反馈信息")
    },
    component: () => import("@/view/feedBack")
  },
  {
    path: "/content",
    name: "content",
    meta: {
      title: "課程內容"
    },
    component: () => import("@/view/content")
  },
  {
    path: "/system",
    name: "system",
    meta: {
      title: "教學體系"
    },
    component: () => import("@/view/system")
  },
  {
    path: "/about2",
    name: "about2",
    meta: {
      title: "關於我们"
    },
    component: () => import("@/view/about2")
  },
  {
    path: "/download",
    name: "download",
    meta: {
      title: "應用程式"
    },
    component: () => import("@/view/download")
  }
];

export default routes;
