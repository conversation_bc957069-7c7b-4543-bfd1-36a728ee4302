spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 5
      maxUnavailable: 0
  template:
    spec:
      dnsConfig:
        options:
          - name: single-request-reopen
          - name: ndots
            value: "2"
      containers:
        - name: ${project.artifactId}
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          imagePullPolicy: Always
          resources:
            requests:
              cpu: 1000m
              memory: 2048Mi
            limits:
              cpu: 1
              memory: 2048Mi
          livenessProbe:
            httpGet:
              path: /liveness.txt
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /readiness.txt
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
      imagePullSecrets:
        - name: harbor-tencent-key
