@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .anim_btn {
    -webkit-tap-highlight-color: transparent;
    @apply active:scale-90 transition cursor-pointer;
  }

  .flex_center {
    @apply flex justify-center items-center;
  }
}


html,
body,
div,
ul,
span,
h1,
h2,
h3,
h4,
h5,
h6,
p,
em,
img,
video {
  margin: 0;
  padding: 0;
}
h1,
h2,
h3,
h4,
h5,
h6,
em,
i {
  font-weight: 100;
  font-style: normal;
}
ul,
ol,
li {
  list-style-type: none;
}
html {
  min-height: 100%;
}
body {
  font-size: 12px;
  color: #333;
  font-family:
    "Helvetica Neue", "Hiragino Sans GB", "Microsoft YaHei", "\9ED1\4F53",
    Arial, sans-serif;
  min-width: 1200px;
  background: #fff;
}
a {
  color: #333;
  text-decoration: none;
}
li {
  list-style: none;
}
em {
  font-style: normal;
}
input {
  border: none;
  outline: none;
  background: none;
}
video {
  object-fit: cover;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.clearfix:after {
  content: "";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
}
.clearfix {
  zoom: 1;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #999;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #999;
}
.sea-main {
  width: 1200px;
  margin: 0 auto;
}
.form-tel {
  position: relative;
}
.form-tel .tel-code {
  font-size: 14px;
  color: #333;
}
.form-tel .tel-code:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background-color: #ededed;
}
.form-email .tel-contact:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background-color: #ededed;
}
.form-tel .tel-arrow {
  width: 8px;
  height: 6px;
  margin-left: 7px;
}
.form-tel .tel-line {
  display: block;
  width: 1px;
  height: 20px;
  background: #d5d5d5;
  margin-top: 13px;
  margin-left: 10px;
  margin-right: 10px;
}

.sea-item-title {
  font-size: 46px;
  color: #333;
  line-height: 48px;
  padding-top: 100px;
}
.sea-item-desc {
  font-size: 26px;
  color: #999;
  line-height: 28px;
  padding-top: 20px;
}
.error-tips {
  position: absolute;
  /* left: -170px; */
  top: 0;
  height: 40px;
  line-height: 40px;
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  background: #ff5938
    url(https://global-static.meishubao.com/sea_web/img/error-icon.png) 16px
    12px no-repeat;
  background-size: 16px 16px;
  border-radius: 6px;
  padding-left: 16px;
  padding-right: 17px;
}
.error-tips span {
  display: inline-block;
  zoom: 1;
  text-indent: 20px;
}

@keyframes mycheck {
  0% {
    -webkit-transform: scale(1);
  }
  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-3deg);
  }
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.2) rotate(3deg);
  }
  40%,
  60%,
  80% {
    -webkit-transform: scale(1.2) rotate(-3deg);
  }
  100% {
    -webkit-transform: scale(1) rotate(0);
  }
}

@-moz-keyframes mycheck /* Firefox */ {
  0% {
    -webkit-transform: scale(1);
  }
  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-3deg);
  }
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.2) rotate(3deg);
  }
  40%,
  60%,
  80% {
    -webkit-transform: scale(1.2) rotate(-3deg);
  }
  100% {
    -webkit-transform: scale(1) rotate(0);
  }
}

@-webkit-keyframes mycheck /* Safari 和 Chrome */ {
  0% {
    -webkit-transform: scale(1);
  }
  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-3deg);
  }
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.2) rotate(3deg);
  }
  40%,
  60%,
  80% {
    -webkit-transform: scale(1.2) rotate(-3deg);
  }
  100% {
    -webkit-transform: scale(1) rotate(0);
  }
}

@-o-keyframes mycheck /* Opera */ {
  0% {
    -webkit-transform: scale(1);
  }
  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-3deg);
  }
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.2) rotate(3deg);
  }
  40%,
  60%,
  80% {
    -webkit-transform: scale(1.2) rotate(-3deg);
  }
  100% {
    -webkit-transform: scale(1) rotate(0);
  }
}

.check-error {
  position: relative;
  width: 14px;
  height: 14px;
  animation: mycheck 0.5s ease;
  /* Firefox: */
  -moz-animation: mycheck 0.5s ease;
  /* Safari 和 Chrome: */
  -webkit-animation: mycheck 0.5s ease;
  /* Opera: */
  -o-animation: mycheck 0.5s ease;
}
