<template>
  <div
    v-if="isShow"
    class="area-code"
    :style="position"
    v-clickoutside="clickoutside"
  >
    <div class="search-input">
      <input v-model="searchCode" type="text" :placeholder="$t('搜索')" />
    </div>
    <div class="area-wrap">
      <div
        class="area-item"
        v-for="(item, index) in codeList"
        :key="index"
        @click="changeCode(item.code)"
      >
        <span class="country">{{ item.zh }}</span>
        <span class="code">+{{ item.code }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import codeData from "@/data/code.js";
export default {
  data() {
    return {
      searchCode: "",
      codeList: codeData,
      position: {}
    };
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    location: {
      type: Number,
      default: 60
    },
    isUp: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    searchCode(val) {
      const pattern1 = new RegExp("[\u4E00-\u9FA5]+");
      const pattern2 = new RegExp("[0-9]+");
      const searchText = val;
      if (searchText == "") {
        this.codeList = codeData;
        return false;
      }
      if (!pattern1.test(searchText) && !pattern2.test(searchText))
        return false;
      let searchArr = [];
      for (let i = 0; i < codeData.length; i++) {
        let item = codeData[i];
        if (
          item.zh.indexOf(searchText) != -1 ||
          item.code.toString().indexOf(searchText) != -1
        ) {
          searchArr.push(item);
        }
      }
      this.codeList = searchArr;
    }
  },
  methods: {
    changeCode(val) {
      this.$emit("changeCode", val);
    },
    clickoutside(val) {
      this.$emit("closePop");
    }
  },
  mounted() {
    if (this.isUp) {
      this.position = { bottom: this.location + "px" };
    } else {
      this.position = { top: this.location + "px" };
    }
  }
};
</script>
<style lang="less" scoped>
.area-code {
  position: absolute;
  left: 0;
  width: 280px;
  font-size: 14px;
  background: rgba(255, 255, 255, 1);
  border-radius: 8px;
  border: 1px solid rgba(234, 234, 234, 1);
  overflow: hidden;
  z-index: 99;
  .search-input {
    width: 240px;
    height: 40px;
    line-height: 40px;
    background: rgba(246, 246, 246, 1);
    border-radius: 8px;
    margin: 18px auto 5px;
    input {
      width: 100%;
      height: 40px;
      padding-left: 20px;
      box-sizing: border-box;
    }
  }
  .area-wrap {
    padding: 0 20px;
    height: 348px;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 4px;
      background: #d8d8d8;
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      background: #fff;
    }
    .area-item {
      color: #666;
      line-height: 40px;
      text-align: left;
      cursor: pointer;
      .code {
        float: right;
      }
    }
  }
}
</style>
