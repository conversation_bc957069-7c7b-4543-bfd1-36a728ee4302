<template>
  <div class="w-full h-[100px] fixed z-[999] transition-all duration-[0.3s] left-0 top-0" :class="{ 'header-bg': !isShow, hk: isHK }">
    <div class="w-[1200px] mx-auto flex justify-between items-center h-full">
      <div class="cursor-pointer w-[260px] h-[44px]" @click="goHome">
        <img class="w-full h-full" src="@/assets/images/home/<USER>/logo.png" />
      </div>
      <ul class="flex items-center justify-between text-white w-[497px] h-[25px] relative">
        <li class="relative" v-for="item in menuArr" :key="item.path" :class="{ active: item.path === path }">
          <router-link :to="item.path">
            <span class="font-semibold text-[18px] text-white leading-[25px]">{{ item.name }}</span>
          </router-link>
        </li>
      </ul>
      <a target="_blank" href="https://wa.me/message/MYEBIQMEMAAFA1">
        <div class="anim_btn select-none w-[170px] h-[50px] rounded-[25px] bg-white flex items-center justify-center cursor-pointer anim_btn">
          <img class="w-[26px] h-[26px] mr-[7px]" src="@/assets/images/home/<USER>/whatsapp.png" alt="" />
          <span class="font-normal text-[14px] text-[#4E4E4E]">Whatsapp查詢</span>
        </div>
      </a>
    </div>
  </div>
</template>
<script>
import { t } from "i18n";
export default {
  data() {
    return {
      menuArr: [
        { name: "首頁", path: "/" },
        { name: "課程內容", path: "/content" },
        { name: "教學體系", path: "/system" },
        { name: "關於我们", path: "/about2" },
        { name: "應用程式", path: "/download" }
      ],
      isShow: true,
      path: "/"
    };
  },
  props: {
    logoType: {
      type: Number,
      default: 0
    },
    showBg: {
      type: Boolean,
      default: true
    },
    isHK: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    let path = this.$route.path;
    window.addEventListener("scroll", this.handleScroll);
    if (path === "/feedBack") {
      this.path = "/";
    } else {
      this.path = path;
    }
    this.isShow = this.showBg;
  },
  methods: {
    handleScroll() {
      let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      if (scrollTop > 90) {
        this.isShow = false;
      } else {
        if (this.$route.path === "/feedBack") {
          this.isShow = false;
        } else {
          this.isShow = true;
        }
      }
    },
    goHome() {
      this.$router.push("/");
    }
  }
};
</script>
<style lang="less" scoped>
.header-bg {
  background: #a250b1;
}
.active {
  position: relative;
  font-weight: 600;
  &:after {
    position: absolute;
    left: 0;
    bottom: -12px;
    content: "";
    width: 100%;
    height: 3px;
    background: #fff;
  }
}
</style>
