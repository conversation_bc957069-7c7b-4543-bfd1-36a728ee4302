<template>
  <div class="sea-foot">
    <div
      class="sea-foot-register"
      v-clickoutside="hideTip"
      :class="{ 'foot-fixed': isShow }"
    >
      <div class="sea-main">
        <img
          src="https://global-static.meishubao.com/sea_web/img/return-icon.png"
          class="return-icon-img fl"
        />
        <div class="return-promise-text fl">
          <p>{{ $t("效果不满意") }}</p>
          <p>{{ $t("120天内无理由退费") }}</p>
        </div>
        <div class="sea-footer-form fr clearfix">
          <div class="form-item form-name fl">
            <input
              type="text"
              class="name-input"
              v-model="username"
              :placeholder="$t('请填写您的姓名')"
              @focus="handleClearName"
              @blur="handleCheckName"
            />
            <div class="error-tips name-tips" v-if="isName">
              <span>{{ nameTips }}</span>
            </div>
          </div>
          <div class="form-item form-tel fl">
            <area-code
              :isShow="showCode"
              :isUp="isUp"
              :location="location"
              @changeCode="changeCode"
              @closePop="closePop"
            ></area-code>
            <div class="tel-code" @click.stop="toggleCode">
              <span>{{ selectCode ? `+${selectCode}` : $t("请选择") }}</span>
            </div>
            <input
              type="text"
              v-model="phone"
              class="tel-input fl"
              :placeholder="$t('请输入手机号')"
              @focus="handleClearPhone"
              @blur="handleCheckPhone"
            />
            <div class="error-tips" v-if="isPhone">
              <span>{{ phoneTips }}</span>
            </div>
          </div>
          <div class="form-item form-email fl">
            <contact
              :isUp="isUp"
              :isShow="showContact"
              :location="location"
              @changeCode="changeContact"
              @closePop="closePopContact"
            ></contact>
            <div class="tel-contact fl" @click.stop="toggleContact">
              <span>{{
                selectContact ? `${selectContact}` : $t("请选择")
              }}</span>
            </div>
            <input
              type="text"
              class="email-input"
              v-model="email"
              :placeholder="
                selectContact === ''
                  ? $t('请输入联系方式')
                  : $t('请输入selectContact', { selectContact: selectContact })
              "
              @focus="handleClearEmail"
              @blur="handleCheckEmail"
            />
            <div class="error-tips email-tips" v-if="isEmail">
              <span>{{ emailTips }}</span>
            </div>
          </div>
          <div class="form-agreement fl clearfix">
            <span class="check-btn fl" @click="handleCheck">
              <img
                src="https://global-static.meishubao.com/sea_web/img/check-default.png"
                v-show="!isAgreement && !isEmpty"
              />
              <img
                src="https://global-static.meishubao.com/sea_web/img/check-select.png"
                v-show="isAgreement"
              />
              <img
                src="https://global-static.meishubao.com/sea_web/img/no-check.png"
                class="check-error"
                v-show="!isAgreement & isEmpty"
              />
            </span>
            <a class="agreement-text fl"
              >{{ $t("我已阅读并同意")
              }}<em @click="showAgreeModal = true">{{ $t("《用户协议》") }}</em
              ><br />{{ $t("与")
              }}<em @click="showSecretModal = true">{{
                $t("《隐私协议》")
              }}</em></a
            >
          </div>
          <div class="form-item form-save fl" @click="handleSave">
            {{ $t("立即免费试听") }}
          </div>
        </div>
      </div>
    </div>
    <div class="sea-main sea-foot-web clearfix">
      <router-link to="/" class="fl"
        ><img
          src="https://global-static.meishubao.com/sea_web/img/foot-logo.png"
          class="foot-logo"
      /></router-link>
      <div class="foot-menu fl clearfix">
        <h3 class="title fl">{{ $t("网站导航") }}</h3>
        <div class="foot-menu-box fl">
          <div class="foot-menu-list">
            <span class="first-item">
              <router-link to="/">{{ $t("网站首页") }}</router-link>
            </span>
            <span>
              <router-link to="/setCourse">{{ $t("课程体系") }}</router-link>
            </span>
          </div>
          <div class="foot-menu-list menu-sec">
            <span class="first-item">
              <router-link to="/teachIntroduction">{{
                $t("师资介绍")
              }}</router-link>
            </span>
            <span>
              <router-link to="/about">{{ $t("关于我们") }}</router-link>
            </span>
          </div>
        </div>
      </div>
      <div class="foot-concat fr clearfix">
        <h3 class="title fl">{{ $t("联系我们") }}</h3>
        <div class="foot-menu-box fl">
          <div class="foot-menu-list">
            <span>E-mail：<EMAIL></span>
          </div>
          <div class="foot-menu-list menu-sec">
            <a
              target="_blank"
              href="https://www.facebook.com/%E7%BE%8E%E6%9C%AF%E5%AE%9D1%E5%AF%B91-111860540302880/?ref=aymt_homepage_panel&eid=ARDYI3q8xhA1pFSQPekiMLKVEjWmrkdIe3dNWJw-QkNqCI9BepDxXkNf5cjDFnhvwHH50cjinp-Et-s0"
            >
              <img
                src="https://global-static.meishubao.com/sea_web/img/facebook-icon.png"
              />
            </a>
            <a
              target="_blank"
              href="https://www.youtube.com/channel/UCrBj9_nJ-6B-5OFXxNK251w"
            >
              <img
                src="https://global-static.meishubao.com/sea_web/img/youtube.png"
              />
            </a>
            <!-- <a
              target="_blank"
              href="https://www.instagram.com/meishubaoglobal/"
            >
              <img
                src="https://global-static.meishubao.com/sea_web/img/ins-icon.png"
              />
            </a> -->
            <a target="_blank" href="https://twitter.com/meishubaoglobal">
              <img
                src="https://global-static.meishubao.com/sea_web/img/tuitetwitter-icon.png"
              />
            </a>
            <!-- <a
              target="_blank"
              href="https://www.pinterest.com/MeishubaoGlobal/"
              class="last-item"
            >
              <img
                src="https://global-static.meishubao.com/sea_web/img/pinterest-icon.png"
              />
            </a> -->
          </div>
        </div>
      </div>
      <!-- <ul class="footer-menu-list fl">
                    <li>
                        网站导航
                    </li>
                    <li>
                        <router-link to="/">网站首页</router-link>
                    </li>
                    <li>
                        <router-link to="/setCourse">课程体系</router-link>
                    </li>
                    <li>
                        <router-link to="/teachIntroduction">师资介绍</router-link>
                    </li>
                    <li>
                        <router-link to="/about">关于我们</router-link>
                    </li>
                </ul>
                <div class="footer-menu-concat fl">
                    <p class="concat-text">联系我们</p>
                    <p class="concat-tel">咨询热线：************</p>
                    <div class="concat-way">
                        <a target="_blank" href="https://twitter.com/meishubaoglobal">
                            <img src="https://global-static.meishubao.com/sea_web/img/tuitetwitter-icon.png" />
                        </a>
                        <a target="_blank" href="https://www.facebook.com/%E7%BE%8E%E6%9C%AF%E5%AE%9D1%E5%AF%B91-111860540302880/?ref=aymt_homepage_panel&eid=ARDYI3q8xhA1pFSQPekiMLKVEjWmrkdIe3dNWJw-QkNqCI9BepDxXkNf5cjDFnhvwHH50cjinp-Et-s0">
                            <img src="https://global-static.meishubao.com/sea_web/img/facebook-icon.png" />
                        </a>
                        <a target="_blank" href="https://www.youtube.com/channel/UCrBj9_nJ-6B-5OFXxNK251w">
                            <img src="https://global-static.meishubao.com/sea_web/img/pinterest-icon.png" />
                        </a>
                        <a target="_blank" href="https://www.instagram.com/meishubaoglobal/">
                            <img src="https://global-static.meishubao.com/sea_web/img/ins-icon.png" />
                        </a>
                    </div>
                </div>
                <div class="footer-menu-download fr clearfix">
                    <div class="download-text fl">下载客户端</div>
                    <div class="download-imgs fl">
                        <a target="_blank" href="#" class="first-img">
                            <img src="https://global-static.meishubao.com/sea_web/img/app-store.png" />
                        </a>
                        <a target="_blank" href="#">
                            <img src="https://global-static.meishubao.com/sea_web/img/google-play.png" />
                        </a>
                    </div>
                </div> -->
    </div>
    <div
      class="sea-foot-copy"
      :style="{ 'padding-bottom': isShow ? '130px' : '29px' }"
    >
      <p class="sea-copy-info copy-company">
        {{ $t("北京艺旗网络科技有限公司") }}
      </p>
      <p class="sea-copy-info copy-prove">
        <!-- <span>浙ICP备18023542号</span> -->
        <span>{{ $t("出版物经营许可证") }}</span>
        <span>{{ $t("增值电信业务经营许可证") }}</span>
        <span>{{ $t("信息系统安全等级保护备案证明") }}</span>
        <a
          class="last-child"
          href="https://vip.meishubao.com/v2/civilizedInternet/"
          target="_blank"
          >{{ $t("文明上网倡议书") }}</a
        >
      </p>
      <p class="sea-copy-info">
        {{ $t("地址：北京市朝阳区朝来高科技产业园区36号院10号楼") }}
      </p>
    </div>
    <agreement-modal
      v-if="showAgreeModal"
      @closeAgreeModal="closeAgreeModal"
    ></agreement-modal>
    <secret-modal
      v-if="showSecretModal"
      @closeAgreeModal="closeAgreeModal"
    ></secret-modal>
  </div>
</template>
<script>
import PhoneNumberUtil from "google-libphonenumber";
const phoneUtil = PhoneNumberUtil.PhoneNumberUtil.getInstance();
import areaCode from "./areaCode";
import contact from "./contact";
import { submitPhone } from "@/api/api";
import agreementModal from "./agreement";
import secretModal from "./secret";
let reg = new RegExp(
  "^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$"
);
import { t } from "i18n";
export default {
  data() {
    return {
      isShow: false,
      selectCode: "",
      selectContact: t("微信"),
      phone: "",
      email: "",
      username: "",
      isEmpty: false,
      isAgreement: true,
      isEmail: false,
      isPhone: false,
      isName: false,
      nameTips: "",
      emailTips: "",
      phoneTips: "",
      location: 50,
      showCode: false,
      showContact: false,
      isUp: true,
      showAgreeModal: false,
      showSecretModal: false
    };
  },
  watch: {
    showAgreeModal(val) {
      if (val) {
        document.body.setAttribute(
          "style",
          `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`
        );
      } else {
        document.body.removeAttribute("style");
      }
    },
    showSecretModal(val) {
      if (val) {
        document.body.setAttribute(
          "style",
          `overflow-y: hidden; margin-right: ${this.$getScrollbarWidth()}px`
        );
      } else {
        document.body.removeAttribute("style");
      }
    }
  },
  components: {
    agreementModal,
    secretModal,
    areaCode,
    contact
  },
  mounted() {
    window.addEventListener("scroll", this.handleScroll);
  },
  methods: {
    handleScroll() {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      let windowHeight =
        document.documentElement.clientHeight || document.body.clientHeight;
      let scrollHeight =
        document.documentElement.scrollHeight || document.body.scrollHeight;
      if (scrollTop > 90 && scrollTop + windowHeight < scrollHeight - 417) {
        this.isShow = true;
      } else {
        this.isShow = false;
      }
    },
    //选择用户协议
    handleCheck() {
      this.isAgreement = !this.isAgreement;
    },
    // 验证手机号
    handleCheckPhone() {
      if (this.phone === "") {
        this.phoneTips = t("手机号不能为空");
        this.isPhone = true;
      } else if (
        this.phone.length < 4 ||
        !phoneUtil.isValidNumber(
          phoneUtil.parseAndKeepRawInput(`+${this.selectCode}${this.phone}`)
        )
      ) {
        this.phoneTips = t("手机号格式不正确");
        this.isPhone = true;
      }
    },
    // 验证邮箱
    handleCheckEmail() {
      if (this.email === "") {
        this.emailTips = t("邮箱不能为空");
        this.isEmail = true;
      } else if (!reg.test(this.email)) {
        this.emailTips = t("邮箱格式有误");
        this.isEmail = true;
      }
    },
    handleCheckName() {
      if (this.username === "") {
        this.nameTips = t("姓名不能为空");
        this.isName = true;
      }
    },
    handleClearPhone() {
      this.phoneTips = "";
      this.isPhone = false;
    },
    handleClearEmail() {
      this.emailTips = "";
      this.isEmail = false;
    },
    handleClearName() {
      this.nameTips = "";
      this.isName = false;
    },
    //保存注册信息
    handleSave() {
      let returnFlag = false;
      if (this.username === "") {
        this.nameTips = t("姓名不能为空");
        this.isName = true;
        returnFlag = true;
      }
      if (this.selectCode === "") {
        this.phoneTips = t("请选择区号");
        this.isPhone = true;
        returnFlag = true;
      } else if (this.phone === "") {
        this.phoneTips = t("手机号不能为空");
        this.isPhone = true;
        returnFlag = true;
      } else if (
        this.phone.length < 4 ||
        !phoneUtil.isValidNumber(
          phoneUtil.parseAndKeepRawInput(`+${this.selectCode}${this.phone}`)
        )
      ) {
        this.phoneTips = t("手机号格式不正确");
        this.isPhone = true;
        returnFlag = true;
      } else {
        this.isPhone = false;
      }
      if (this.email === "") {
        this.emailTips = t("联系方式不能为空");
        this.isEmail = true;
        returnFlag = true;
      } else if (this.selectContact === t("邮箱")) {
        if (!reg.test(this.email)) {
          this.emailTips = t("邮箱格式有误");
          this.isEmail = true;
          returnFlag = true;
        }
      } else {
        this.isEmail = false;
      }
      if (!this.isAgreement) {
        this.isEmpty = true;
        setTimeout(() => {
          this.isEmpty = false;
        }, 500);
        returnFlag = true;
      }
      if (returnFlag) return false;
      let params = {
        is_join: 1,
        channel: this.$route.query.channel || "2144342",
        adplatform: this.$route.query.adplatform || "",
        bd_vid: this.$route.query.bd_vid || "",
        mcode: this.selectCode,
        mobile: this.phone,
        name: this.username,
        orther: `${this.selectContact}：${this.email}`
      };
      submitPhone(params).then(res => {
        if (res.data.code === 200) {
          if (res.data.is_first) {
            window.reportAd &&
              window.reportAd({
                email: this.selectContact === t("邮箱") ? this.email : "",
                phone_number: this.phone
              });
            this.$router.push({
              path: "/feedBack"
            });
          } else {
            this.$toast(res.data.msg);
          }
        } else {
          this.$toast(res.data.msg);
        }
      });
    },
    changeCode(val) {
      this.selectCode = val;
      this.showCode = false;
    },
    changeContact(val) {
      this.selectContact = val;
      this.showContact = false;
    },
    closePop() {
      this.showCode = false;
    },
    closePopContact() {
      this.showContact = false;
    },
    toggleCode(e) {
      this.closePopContact();
      this.showCode = !this.showCode;
      this.location = e.target.getBoundingClientRect().height + 2;
    },
    toggleContact(e) {
      this.closePop();
      this.showContact = !this.showContact;
      this.location = e.target.getBoundingClientRect().height + 2;
    },
    closeAgreeModal() {
      this.showAgreeModal = false;
      this.showSecretModal = false;
    },
    toggleCode() {
      this.showCode = !this.showCode;
    },
    closePop() {
      this.showCode = false;
    },
    hideTip() {
      this.isName = false;
      this.isPhone = false;
      this.isEmail = false;
    }
  }
};
</script>
<style lang="less" scoped>
.sea-foot {
  width: 100%;
  .sea-foot-register {
    width: 100%;
    height: 90px;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999;
  }
  .foot-fixed {
    position: fixed;
    left: 0;
    bottom: 0;
  }
  .return-icon-img {
    width: 56px;
    height: 66px;
    margin-top: 12px;
  }
  .return-promise-text {
    margin-left: 17px;
    margin-top: 23px;
    p {
      font-size: 16px;
      color: #ffe67f;
      line-height: 22px;
    }
  }
  .sea-footer-form {
    margin-top: 22px;
    .form-item {
      position: relative;
      height: 46px;
      line-height: 46px;
      background: #fff;
      border-radius: 23px;
      margin-right: 12px;
      input {
        height: 46px;
        line-height: 46px;
      }
      .error-tips {
        left: 50%;
        top: -54px;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        &::after {
          position: absolute;
          left: 50%;
          bottom: -16px;
          display: block;
          margin-left: -5px;
          content: "";
          width: 0;
          height: 0;
          border-style: solid;
          border-top: 10px #ff5938 dashed;
          border-right: 10px transparent dashed;
          border-bottom: 10px transparent dashed;
          border-left: 10px transparent solid;
          border-radius: 2px;
        }
      }
      .email-tips {
        left: 50%;
        top: -54px;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        text-align: left;
      }
    }
    .form-agreement {
      margin-right: 17px;
      .check-btn {
        width: 14px;
        height: 14px;
        margin-top: 16px;
        img {
          width: inherit;
          height: inherit;
        }
      }
      .agreement-text {
        font-size: 12px;
        font-weight: 400;
        color: #fff;
        line-height: 14px;
        margin-left: 8px;
        margin-top: 8px;
        em {
          color: #7fc1ff;
          cursor: pointer;
        }
      }
    }
    .form-tel,
    .form-email {
      position: relative;
      width: 220px;
      .tel-code,
      .tel-contact {
        position: absolute;
        left: 0;
        top: 0;
        width: 71px;
        text-align: center;
        cursor: pointer;
        span {
          font-size: 14px;
          display: inline-block;
          position: relative;
          padding-right: 12px;
          height: 46px;
          &:after {
            content: "";
            position: absolute;
            right: 0;
            top: 21px;
            width: 8px;
            height: 6px;
            background: url("https://global-static.meishubao.com/sea_web/img/form-more.png")
              no-repeat;
            background-size: 100% 100%;
            transition: all 0.3s;
          }
          &.rotate:after {
            transform: rotate(180deg);
          }
        }
      }
      .tel-input {
        width: 118px;
        margin-left: 82px;
        padding-right: 20px;
      }
      .email-input {
        width: 118px;
        margin-left: 82px;
        padding-right: 20px;
      }
    }
    .form-email {
      width: 220px;
      margin-right: 20px;
      input {
        width: 180px;
        padding: 0 20px;
      }
    }
    .form-name {
      width: 140px;
      input {
        width: 100px;
        padding: 0 20px;
      }
    }
    .form-save {
      width: 150px;
      font-size: 16px;
      color: #fff;
      text-align: center;
      font-weight: 400;
      background: #ff9d2c;
      margin-right: 0;
      cursor: pointer;
    }
  }
  .sea-foot-web {
    margin-top: 46px;
    margin-bottom: 56px;
    .foot-logo {
      width: 175px;
      height: 73px;
    }
    .menu-sec {
      margin-top: 22px;
    }
    .foot-menu {
      margin-left: 130px;
      .foot-menu-box {
        margin-left: 65px;

        span {
          a {
            font-size: 18px;
            font-weight: 400;
            color: #888;
            line-height: 20px;
          }
        }
        .first-item {
          margin-right: 45px;
        }
      }
    }
    .foot-concat {
      margin-left: 49px;
      span {
        font-size: 18px;
        font-weight: 400;
        color: #888;
        line-height: 20px;
      }
      .foot-menu-box {
        margin-left: 49px;
      }
      .foot-menu-list {
        .last-item {
          margin-right: 0 !important;
        }
        a {
          width: 50px;
          height: 50px;
          margin-right: 20px;
          display: inline-block;
          img {
            width: inherit;
            height: inherit;
          }
        }
      }
    }
    .title {
      position: relative;
      font-size: 18px;
      font-weight: 400;
      color: #333;
      line-height: 20px;
      &::after {
        display: block;
        content: "";
        position: absolute;
        left: 0;
        bottom: -16px;
        width: 36px;
        height: 4px;
        background: #ff5938;
      }
    }
    // margin-top: 67px;
    // .footer-menu-list {
    //     li {
    //         font-size: 20px;
    //         color: #888888;
    //         line-height: 20px;
    //         margin-bottom: 32px;
    //         a {
    //             color: #888;
    //         }
    //     }
    // }
    // .footer-menu-concat {
    // text-align: left;
    //     margin-left: 318px;
    //     .concat-text,.concat-tel {
    //         font-size: 18px;
    //         color: #888888;
    //         line-height: 20px;
    //     }
    //     .concat-tel {
    //         margin-top: 32px;
    //         margin-bottom: 36px;
    //     }
    //     .concat-way {
    //         img {
    //             width: 50px;
    //             height: 50px;
    //             margin-right: 29px;
    //         }
    //     }
    // }
    // .footer-menu-download {
    //     .download-text {
    //         color: #888888;
    //         font-size: 18px;
    //         line-height: 20px;
    //     }
    //     .download-imgs {
    //         margin-left: 32px;
    //         a {
    //             display: block;
    //             width: 230px;
    //             height: 68px;
    //             img {
    //                 width: 230px;
    //                 height: 68px;
    //             }
    //         }
    //         .first-img {
    //             margin-bottom: 26px;
    //         }
    //     }
    // }
  }

  .sea-foot-copy {
    width: 100%;
    background: #f7f7f7;
    text-align: center;
    padding-bottom: 29px;
    .sea-copy-info {
      font-size: 16px;
      color: #999;
      line-height: 20px;
      a {
        font-size: 16px;
        color: #999;
        line-height: 20px;
      }
    }
    .copy-company {
      padding-top: 22px;
    }
    .copy-prove {
      padding-top: 9px;
      padding-bottom: 10px;
    }
    .copy-prove span {
      margin-right: 30px;
    }
    .copy-prove {
      .last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
