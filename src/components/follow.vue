<template>
  <a target="_blank" href="https://wa.me/message/MYEBIQMEMAAFA1">
    <div class="w-[60px] h-[60px] flex justify-center items-center bg-white rounded-[12px] fixed right-[20px] top-[50%]" @mouseenter="setShow(true)" @mouseleave="setShow(false)" @mousemove="setShow(true)">
      <img class="w-[26px] h-[26px]" src="@/assets/images/home/<USER>/web/whatsapp图标@2x.png" alt="" />
      <!-- <div v-if="show" class="w-[155px] h-[174px] absolute left-[-165px] top-[-35px]">
      <div class="relative z-10 ml-[20px] mt-[20px] w-[104px] h-[104px] rounded-[6px] bg-[#EEEEEE]"></div>
      <img class="w-full h-full absolute z-0 top-0 left-0" src="@/assets/images/home/<USER>/web/编组 <EMAIL>" alt="" />
    </div> -->
    </div>
  </a>
</template>
<script>
export default {
  data() {
    return {
      show: false
    };
  },
  components: {},
  methods: {
    setShow(show) {
      this.show = show;
    }
  }
};
</script>
<style lang="less" scoped></style>
