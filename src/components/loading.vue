<template>
  <div v-if="showLoading" class="loading">
    <div class="loading-icon"></div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    showLoading: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style lang="less" scoped>
.loading {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.05);
  .loading-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #2d8cf0;
    animation: ani-spin-bounce 1s 0s ease-in-out infinite;
  }
}
@keyframes ani-spin-bounce {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  to {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }
}
</style>
