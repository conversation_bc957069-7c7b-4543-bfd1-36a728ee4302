<template>
  <div
    v-if="isShow"
    class="contact"
    :style="position"
    v-clickoutside="clickoutside"
  >
    <div class="contact-wrap">
      <div class="contact-item" @click="changeCode($t('微信'))">
        <span class="country">{{ $t("微信") }}</span>
      </div>
      <div class="contact-item" @click="changeCode($t('邮箱'))">
        <span class="country">{{ $t("邮箱") }}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      searchCode: "",
      position: {}
    };
  },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    location: {
      type: Number,
      default: 60
    },
    isUp: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    changeCode(val) {
      this.$emit("changeCode", val);
    },
    clickoutside(val) {
      this.$emit("closePop");
    }
  },
  mounted() {
    if (this.isUp) {
      this.position = { bottom: this.location + "px" };
    } else {
      this.position = { top: this.location + "px" };
    }
  }
};
</script>
<style lang="less" scoped>
.contact {
  position: absolute;
  left: 0;
  width: 70px;
  font-size: 14px;
  background: rgba(255, 255, 255, 1);
  border-radius: 8px;
  border: 1px solid rgba(234, 234, 234, 1);
  z-index: 9;
  .search-input {
    width: 70px;
    height: 40px;
    line-height: 40px;
    background: rgba(246, 246, 246, 1);
    border-radius: 8px;
    margin: 18px auto 5px;
    input {
      width: 100%;
      height: 40px;
      padding-left: 20px;
    }
  }
  .contact-wrap {
    padding-right: 10px;
    padding-left: 15px;
    width: 60px;
    height: 108px;
    margin: 0 auto;
    box-sizing: border-box;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 4px;
      background: #d8d8d8;
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      background: #fff;
    }
    .contact-item {
      color: #666;
      line-height: 40px;
      text-align: left;
      cursor: pointer;
      .code {
        float: right;
      }
    }
  }
}
</style>
