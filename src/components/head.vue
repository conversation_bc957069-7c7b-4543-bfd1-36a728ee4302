<template>
  <div class="sea-header" :class="{ 'header-bg': !isShow, hk: isHK }">
    <div class="sea-header-item clearfix">
      <div class="sea-header-logo fl" @click="goHome">
        <img
          v-if="isShow && logoType == 0"
          src="https://global-static.meishubao.com/sea_web/img/header-logo-default.png"
        />
        <img
          v-if="isShow && logoType == 1"
          src="https://global-static.meishubao.com/sea_web/img/header-logo1.png"
        />
        <img
          v-if="!isShow"
          src="https://global-static.meishubao.com/sea_web/img/header-logo.png"
        />
      </div>
      <ul class="sea-header-menu fr" :class="{ open: isShow }">
        <li
          v-for="item in menuArr"
          :key="item.path"
          :class="{ active: item.path === path }"
        >
          <router-link :to="item.path">{{ item.name }}</router-link>
        </li>
        <!-- <li class="sea-login"><a href="https://vip.meishubao.com/student/login/" target="_blank">登录</a></li> -->
      </ul>
    </div>
  </div>
</template>
<script>
import { t } from "i18n";
export default {
  data() {
    return {
      menuArr: [
        { name: t("首页"), path: "/" },
        { name: t("师资介绍"), path: "/teachIntroduction" },
        { name: t("课程体系"), path: "/setCourse" },
        // {"name": "下载中心","path": "/downloadCenter"},
        { name: t("关于我们"), path: "/about" }
      ],

      isShow: true,
      path: "/"
    };
  },
  props: {
    logoType: {
      type: Number,
      default: 0
    },
    showBg: {
      type: Boolean,
      default: true
    },
    isHK: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    let path = this.$route.path;
    window.addEventListener("scroll", this.handleScroll);
    if (path === "/feedBack") {
      this.path = "/";
    } else {
      this.path = path;
    }
    this.isShow = this.showBg;
  },
  methods: {
    handleScroll() {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (scrollTop > 90) {
        this.isShow = false;
      } else {
        if (this.$route.path === "/feedBack") {
          this.isShow = false;
        } else {
          this.isShow = true;
        }
      }
    },
    goHome() {
      this.$router.push("/");
    }
  }
};
</script>
<style lang="less" scoped>
.sea-header {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 90px;
  z-index: 999;
  transition: all 0.3s;
  .open {
    .sea-login {
      a {
        border: 1px solid #fff;
      }
    }
    li {
      color: #fff;
      a {
        color: #fff;
      }
    }
    .active {
      &:after {
        background: #fff;
      }
    }
  }
}
.hk {
  top: 40px !important;
}
.header-bg {
  background: #fff;
}
.sea-header-item {
  padding-left: 40px;
  padding-right: 45px;
  .sea-header-logo {
    width: 175px;
    height: 90px;
    line-height: 90px;
    cursor: pointer;
  }
  .sea-header-logo img {
    width: 175px;
    height: 34px;
    margin-top: 28px;
  }
}
.sea-header-menu {
  li {
    position: relative;
    float: left;
    font-size: 16px;
    font-weight: 400;
    color: #333;
    line-height: 90px;
    margin-left: 40px;
  }
  .active {
    position: relative;
    font-weight: 600;
    &:after {
      position: absolute;
      left: 4px;
      bottom: 23px;
      content: "";
      width: 80%;
      height: 2px;
      background: #ff3300;
    }
  }
  .sea-login {
    a {
      display: inline-block;
      width: 120px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #ff3300;
      border-radius: 20px;
      border: 1px solid #ff3300;
      cursor: pointer;
    }
  }
}
</style>
