<template>
  <div class="video-play-box" @click.self="closeVideo">
    <div class="video-play">
      <video
        id="index_video1"
        autoplay="autoplay"
        muted="muted"
        controls="controls"
      >
        <source :src="videoSrc" type="video/mp4" />
      </video>
    </div>
  </div>
</template>
<script>
export default {
  name: "videoPlay",
  props: {
    videoSrc: {
      type: String
    }
  },
  data() {
    return {};
  },
  methods: {
    closeVideo() {
      this.$emit("closed", false);
    }
  }
};
</script>
<style lang="less" scoped>
.video-play-box {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  .video-play {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    width: 800px;
    height: auto;
    video {
      width: inherit;
      height: auto;
    }
  }
}
</style>
