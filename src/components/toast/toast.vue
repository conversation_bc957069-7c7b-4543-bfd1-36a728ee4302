<template>
  <transition name="fade">
    <div class="toast-tip" v-show="visible">
      <img
        class="close"
        @click="closeTip"
        src="https://global-static.meishubao.com/sea_web/img/close_icon.png"
        alt=""
      />
      {{ message }}
    </div>
  </transition>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      message: ""
    };
  },
  methods: {
    closeTip() {
      this.visible = false;
    }
  }
};
</script>

<style scoped lang="less">
.toast-tip {
  position: fixed;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  max-width: 360px;
  padding: 12px 26px;
  font-size: 18px;
  line-height: 25px;
  color: #333;
  background: #fff;
  box-shadow: 0px 4px 8px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
